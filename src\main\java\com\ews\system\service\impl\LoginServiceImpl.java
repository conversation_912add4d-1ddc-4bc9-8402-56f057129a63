package com.ews.system.service.impl;

import java.text.ParseException;

import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpSession;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.subject.Subject;
import org.apache.shiro.util.Assert;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.stereotype.Service;

import com.ews.common.EncryptUtils;
import com.ews.common.LoginResult;
import com.ews.config.jwt.JWTUtil;
import com.ews.system.entity.LoginUser;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;

@Service
public class LoginServiceImpl implements LoginService {

	@Autowired
	LoginResult loginResult;
	@Autowired
	private UserService userService;

	@Override
	public LoginResult login(String userName, String password) {
		Assert.notNull(userName, "User name cannot be empty");
		Assert.notNull(password, "Password cannot be empty");

		User user = userService.findByUserName(userName);
		loginResult.setLogin(false);
		
		if(user==null) {
			loginResult.setResult("Incorrect username");
			return loginResult;
		}
		if(user.getIsAvailable().intValue() != 1 ) {
			loginResult.setResult("The user is currently unable to log in to the system");
		}else {
			String md5Pwd = "";
			try {
				md5Pwd = EncryptUtils.encrypt(password, user.getCredentialsSalt());
			} catch (ParseException e1) {
				e1.printStackTrace();
			}

			if(!md5Pwd.equals(user.getPassword())) {
				
				//System.out.println("******************************************md5Pwd:"+md5Pwd);
				//System.out.println("******************************************user.getPassword()："+user.getPassword());
				
				loginResult.setResult("Incorrect username or password");	
				return loginResult;
			}
			
			loginResult.setLogin(true);	
			loginResult.setResult(JWTUtil.sign(userName, md5Pwd));
		}
		
		return loginResult;
	}

	@Override
	public void logout() {
		Subject subject = SecurityUtils.getSubject();
		subject.logout();
	}

	@Override
	public String getCurrentUserName() {
		
		if(SecurityUtils.getSubject().getPrincipal()!=null) {
			String token = SecurityUtils.getSubject().getPrincipal().toString();
			
			return JWTUtil.getUsername(token);
		}
		return null;
	}

	@Override
	public LoginResult frontLogin(String userName, String password, HttpServletRequest request) {
		// TODO Auto-generated method stub

		if (userName == null || userName.isEmpty()) {
			loginResult.setLogin(false);
			loginResult.setResult("User name is empty");
			loginResult.setErrorType(0);
			return loginResult;
		}
		String msg = "";
		Integer errType = 0;
		User user = userService.findByUserName(userName);

		if (user != null && user.getIsDeleted().equals(0)) {
			if (user.getIsAvailable().equals(1)) {// 活跃状态用户
				String md5Pwd = "";
				try {
					md5Pwd = EncryptUtils.encrypt(password, user.getCredentialsSalt());
				} catch (ParseException e1) {
					// TODO Auto-generated catch block
					e1.printStackTrace();
					msg = "User authentication failed";// 3系统异常验证失败
					errType = 3;
				}
				// 验证
				if (user.getPassword().equals(md5Pwd)) {
					loginResult.setLogin(true);
					loginResult.setResult(user.getNickName());

					HttpSession session = request.getSession();
					LoginUser model = new LoginUser();
					model.setNickName(user.getNickName());
					model.setUserId(user.getUserId());
					model.setUsername(user.getUsername());
					session.setAttribute("user", model);
					return loginResult;
				} else {
					msg = "Password error:";// 2密码错误
					errType = 2;
				}
			} else {
				msg = "The user has been disabled, please contact the administrator";// 4已禁用
				errType = 4;
			}
		} else {
			msg = "Account does not exist";// 1
			errType = 1;
		}
		loginResult.setLogin(false);
		loginResult.setResult(msg);
		loginResult.setErrorType(errType);

		return loginResult;
	}

}
