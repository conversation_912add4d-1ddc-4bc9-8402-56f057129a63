# CRM系统顶部导航栏标准规范

## 概述

本文档定义了CRM系统所有页面顶部导航栏的标准规范，包括HTML结构、CSS样式、JavaScript功能和响应式设计。所有新页面必须按照此标准实现顶部导航栏，以确保整个系统的一致性和用户体验。

## 1. HTML结构标准

### 1.1 完整HTML结构

```html
<!-- 顶部导航栏 -->
<nav class="navbar">
  <div class="nav-left">
    <!-- 系统Logo -->
    <div class="nav-logo">
      <img th:src="${CRM_LOGO1}" alt="USK">
    </div>
    
    <!-- 系统标题 -->
    <div class="nav-title" style="color: #ffffff;">[[${CRM_TITLE}]]</div>
    
    <!-- 桌面端导航菜单 -->
    <div class="nav-menu">
      <a href="main" class="nav-item active" th:text="#{menu.home}">Home</a>
      <a href="deposit" class="nav-item" th:text="#{menu.fundtransfer}">Fund Transfer</a>
      <a href="fundList" class="nav-item" th:text="#{menu.fundrecords}">Fund Records</a>
      <a href="orderList" class="nav-item" th:text="#{menu.traderecords}">Trade Records</a>
      <a th:if="${WEB_TRADER_STATUS} eq '1'" th:href="${WEB_TRADER_URL}" target="_blank" class="nav-item" th:text="#{menu.webtrader}">WebTrader</a>
      <a href="profile" class="nav-item" th:text="#{menu.profile}">Profile</a>
      <a th:if="${SUPPORT_STATUS} eq '1'" th:href="${SupportCenter}" target="_blank" class="nav-item" th:text="#{menu.support}">Support</a>
    </div>
  </div>
  
  <div class="nav-right">
    <!-- 用户信息显示 -->
    <div class="nav-user">
      <span>[[${userInfo.userName}]]</span>
    </div>
            
    <!-- 语言切换下拉菜单 -->
    <div class="nav-item dropdown language-dropdown">
      <a href="#" class="nav-item" data-bs-toggle="dropdown" th:aria-label="#{main.language}" style="padding: 6px 12px; display: flex; align-items: center; gap: 6px; flex-shrink: 0;">
        <img src="../img/Langauge.png" width="14" height="14"/>
        <span style="font-size: 12px; white-space: nowrap;" th:text="#{main.language}">语言</span>
      </a>
      <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
        <a href="javascript:updateaLang('zh-cn');" class="dropdown-item">简体中文</a>
        <a href="javascript:updateaLang('zh-tw');" class="dropdown-item">繁體中文</a>
        <a href="javascript:updateaLang('en');" class="dropdown-item">English</a>
        <a href="javascript:updateaLang('th');" class="dropdown-item">ภาษาไทย</a>
        <a href="javascript:updateaLang('ms');" class="dropdown-item">Bahasa Malay</a>
        <a href="javascript:updateaLang('id');" class="dropdown-item">Bahasa Indonesia</a>
        <a href="javascript:updateaLang('vi');" class="dropdown-item">Tiếng Việt</a>
        <a href="javascript:updateaLang('ja');" class="dropdown-item">日本語</a>
      </div>
    </div>
    
    <!-- 移动端菜单按钮 -->
    <div class="mobile-menu-btn" onclick="toggleMobileMenu()">
      <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
        <line x1="3" y1="6" x2="21" y2="6"/>
        <line x1="3" y1="12" x2="21" y2="12"/>
        <line x1="3" y1="18" x2="21" y2="18"/>
      </svg>
    </div>
    
    <!-- 桌面端操作按钮 -->
    <div th:if="${isMobile==0}" style="display: flex; gap: 12px;">
      <a href="modifyPwd" class="action-icon-btn modify-pwd" th:title="#{main.modifypassword}">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
          <circle cx="12" cy="16" r="1"/>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
        </svg>
      </a>
      <a href="logout" class="action-icon-btn logout" th:title="#{main.logout}">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
          <polyline points="16,17 21,12 16,7"/>
          <line x1="21" y1="12" x2="9" y2="12"/>
        </svg>
      </a>
    </div>
    
    <!-- 移动端下拉菜单 -->
    <div id="mobileMenu" class="mobile-menu">
      <a href="main">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M3 9l9-7 9 7v11a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2z"/>
          <polyline points="9,22 9,12 15,12 15,22"/>
        </svg>
        <span th:text="#{menu.home}">首页</span>
      </a>
      <a href="deposit">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <line x1="12" y1="1" x2="12" y2="23"/>
          <path d="M17 5H9.5a3.5 3.5 0 0 0 0 7h5a3.5 3.5 0 0 1 0 7H6"/>
        </svg>
        <span th:text="#{menu.fundtransfer}">资金转账</span>
      </a>
      <a href="fundList">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="4" width="18" height="18" rx="2" ry="2"/>
          <line x1="16" y1="2" x2="16" y2="6"/>
          <line x1="8" y1="2" x2="8" y2="6"/>
          <line x1="3" y1="10" x2="21" y2="10"/>
        </svg>
        <span th:text="#{menu.fundrecords}">资金记录</span>
      </a>
      <a href="orderList">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M14 2H6a2 2 0 0 0-2 2v16a2 2 0 0 0 2 2h12a2 2 0 0 0 2-2V8z"/>
          <polyline points="14,2 14,8 20,8"/>
          <line x1="16" y1="13" x2="8" y2="13"/>
          <line x1="16" y1="17" x2="8" y2="17"/>
        </svg>
        <span th:text="#{menu.traderecords}">交易记录</span>
      </a>
      <a th:if="${WEB_TRADER_STATUS} eq '1'" th:href="${WEB_TRADER_URL}" target="_blank">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <polyline points="22,12 18,12 15,21 9,3 6,12 2,12"/>
        </svg>
        <span th:text="#{menu.webtrader}">网页交易</span>
      </a>
      <a href="profile">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M20 21v-2a4 4 0 0 0-4-4H8a4 4 0 0 0-4 4v2"/>
          <circle cx="12" cy="7" r="4"/>
        </svg>
        <span th:text="#{menu.profile}">个人资料</span>
      </a>
      <a th:if="${SUPPORT_STATUS} eq '1'" th:href="${SupportCenter}" target="_blank">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <circle cx="12" cy="12" r="10"/>
          <path d="M9.09 9a3 3 0 0 1 5.83 1c0 2-3 3-3 3"/>
          <line x1="12" y1="17" x2="12.01" y2="17"/>
        </svg>
        <span th:text="#{menu.support}">客户支持</span>
      </a>
      <hr style="border: 0; border-top: 1px solid rgba(255, 255, 255, 0.1); margin: 5px 0;">
      <a href="modifyPwd">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
          <circle cx="12" cy="16" r="1"/>
          <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
        </svg>
        <span th:text="#{main.modifypassword}">修改密码</span>
      </a>
      <a href="logout">
        <svg width="16" height="16" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2">
          <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
          <polyline points="16,17 21,12 16,7"/>
          <line x1="21" y1="12" x2="9" y2="12"/>
        </svg>
        <span th:text="#{main.logout}">退出登录</span>
      </a>
    </div>
  </div>
</nav>
```

### 1.2 结构说明

| 组件 | 类名 | 描述 | 必需 |
|------|------|------|------|
| 导航栏容器 | `.navbar` | 顶部导航栏主容器 | ✅ |
| 左侧区域 | `.nav-left` | 包含Logo、标题和主导航 | ✅ |
| Logo容器 | `.nav-logo` | 系统Logo显示区域 | ✅ |
| 系统标题 | `.nav-title` | 动态系统标题 | ✅ |
| 主导航菜单 | `.nav-menu` | 桌面端主要导航项 | ✅ |
| 右侧区域 | `.nav-right` | 包含用户信息和操作 | ✅ |
| 用户信息 | `.nav-user` | 用户名显示区域 | ✅ |
| 语言选择器 | `.language-dropdown` | 多语言切换下拉菜单 | ✅ |
| 移动端菜单按钮 | `.mobile-menu-btn` | 汉堡菜单触发按钮 | ✅ |
| 桌面端操作按钮 | `.action-icon-btn` | 修改密码和退出登录按钮 | ✅ |
| 移动端菜单 | `.mobile-menu` | 移动端下拉导航菜单 | ✅ |

## 2. CSS样式标准

### 2.1 基础样式

```css
/* 全局重置 */
* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

/* 主体样式 */
body {
    font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
    background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
    color: #ffffff;
    overflow-x: hidden;
}
```

### 2.2 导航栏主体样式

```css
/* 顶部导航栏 */
.navbar {
    background: rgba(26, 31, 46, 0.95);
    backdrop-filter: blur(20px);
    border-bottom: 1px solid rgba(0, 122, 255, 0.2);
    padding: 0 20px;
    min-height: 70px;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: sticky;
    top: 0;
    z-index: 1000;
    flex-wrap: nowrap;
    overflow: visible;
}

.nav-left {
    display: flex;
    align-items: center;
    flex: 1;
    min-width: 0;
}

.nav-right {
    display: flex;
    align-items: center;
    gap: 15px;
    flex-shrink: 0;
    overflow: visible !important;
    position: relative;
}
```

### 2.3 Logo和标题样式

```css
.nav-logo {
    width: 40px;
    height: 40px;
    border-radius: 8px;
    display: flex;
    align-items: center;
    justify-content: center;
    margin-right: 15px;
    overflow: hidden;
}

.nav-logo img {
    width: 100%;
    height: 100%;
    object-fit: contain;
}

.nav-title {
    font-size: 18px;
    font-weight: 500;
    letter-spacing: 1px;
    color: #ffffff;
    white-space: nowrap;
    flex-shrink: 0;
}
```

### 2.4 导航菜单样式

```css
.nav-menu {
    display: flex;
    align-items: center;
    gap: 15px;
    margin-left: 30px;
    flex: 1;
    min-width: 0;
    overflow: visible;
    padding: 5px 0;
}

.nav-item {
    color: #ffffff;
    text-decoration: none;
    padding: 8px 12px;
    border-radius: 8px;
    transition: all 0.3s ease;
    font-size: 13px;
    white-space: nowrap;
    flex-shrink: 0;
}

.nav-item:hover, .nav-item.active {
    color: #007AFF;
    background: rgba(0, 122, 255, 0.1);
}

/* 导航菜单滚动条样式 */
.nav-menu::-webkit-scrollbar {
    height: 4px;
}

.nav-menu::-webkit-scrollbar-track {
    background: rgba(255, 255, 255, 0.1);
    border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb {
    background: rgba(0, 122, 255, 0.5);
    border-radius: 2px;
}

.nav-menu::-webkit-scrollbar-thumb:hover {
    background: rgba(0, 122, 255, 0.7);
}
```

### 2.5 用户信息样式

```css
.nav-user {
    display: flex;
    align-items: center;
    gap: 8px;
    padding: 6px 12px;
    background: rgba(0, 122, 255, 0.1);
    border-radius: 20px;
    cursor: pointer;
    flex-shrink: 0;
}

.nav-user span {
    white-space: nowrap;
    font-size: 13px;
    color: #ffffff;
}
```

### 2.6 语言下拉菜单样式

```css
/* 语言下拉菜单样式 */
.language-dropdown {
    position: relative !important;
    z-index: 9999;
}

.language-dropdown .dropdown-menu {
    z-index: 10000 !important;
    position: absolute !important;
    top: 100% !important;
    right: 0 !important;
    left: auto !important;
    background: rgba(26, 31, 46, 0.98) !important;
    backdrop-filter: blur(20px) !important;
    border: 1px solid rgba(0, 122, 255, 0.3) !important;
    border-radius: 8px !important;
    min-width: 180px !important;
    margin-top: 8px !important;
    box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4) !important;
    transform: none !important;
}

.language-dropdown .dropdown-item {
    color: rgba(255, 255, 255, 0.8) !important;
    padding: 10px 16px !important;
    font-size: 14px !important;
    border-bottom: 1px solid rgba(255, 255, 255, 0.1) !important;
    transition: all 0.2s ease !important;
}

.language-dropdown .dropdown-item:hover {
    background: rgba(0, 122, 255, 0.15) !important;
    color: rgba(255, 255, 255, 1) !important;
}

.language-dropdown .dropdown-item:last-child {
    border-bottom: none !important;
}

/* 确保所有下拉菜单都显示在最上层 */
.dropdown-menu.show {
    z-index: 10000 !important;
}

.navbar .dropdown {
    z-index: 9999;
    position: relative;
}

.navbar .dropdown-menu {
    z-index: 10000;
}

.language-dropdown .dropdown-menu.show {
    display: block !important;
    opacity: 1 !important;
    visibility: visible !important;
}
```

### 2.7 操作按钮样式

```css
/* 操作按钮样式 */
.action-icon-btn {
    padding: 10px;
    display: flex;
    align-items: center;
    justify-content: center;
    border-radius: 10px;
    transition: all 0.3s ease;
    text-decoration: none;
    border: none;
    backdrop-filter: blur(10px);
    position: relative;
    overflow: hidden;
}

.action-icon-btn.modify-pwd {
    background: rgba(0, 122, 255, 0.2);
}

.action-icon-btn.modify-pwd:hover {
    background: rgba(0, 122, 255, 0.35);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
}

.action-icon-btn.logout {
    background: rgba(255, 59, 48, 0.2);
}

.action-icon-btn.logout:hover {
    background: rgba(255, 59, 48, 0.35);
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(255, 59, 48, 0.4);
}

.action-icon-btn svg {
    color: rgba(255, 255, 255, 0.9);
    transition: all 0.3s ease;
}

.action-icon-btn:hover svg {
    color: rgba(255, 255, 255, 1);
    transform: scale(1.1);
}

.action-icon-btn.modify-pwd svg {
    color: #007AFF;
}

.action-icon-btn.logout svg {
    color: #FF3B30;
}
```

### 2.8 移动端菜单样式

```css
/* 默认隐藏移动端菜单按钮和菜单 */
.mobile-menu-btn {
    display: none;
}

.mobile-menu {
    display: none !important;
}

/* 桌面端和移动端用户信息切换 */
.mobile-user {
    display: none;
}

.desktop-user {
    display: inline;
}

/* 大屏幕强制隐藏移动端菜单 */
@media (min-width: 769px) {
    .mobile-menu-btn {
        display: none !important;
    }
    
    .mobile-menu {
        display: none !important;
    }
}
```

### 2.10 移动端视觉规范（新版）

```css
/* 移动端顶部视觉 - 与最新 withdraw/deposit 一致 */
@media (max-width: 768px) {
  .navbar { min-height: 60px; padding: 10px 16px; }
  .nav-left { display: flex; align-items: center; gap: 12px; }
  .nav-logo { width: 36px; height: 36px; margin-right: 8px; }
  .nav-title { display: none; }
  .nav-right { gap: 12px; }
  .nav-user { background: transparent; padding: 0; border-radius: 0; }
  .nav-user span { font-size: 16px; color: #fff; }
  .desktop-user { display: none; }
  .mobile-user { display: inline; }
  .language-dropdown .nav-item { padding: 6px 8px !important; }
  .language-dropdown .nav-item img { width: 16px; height: 16px; }

  /* 汉堡按钮：圆角描边 + 内外发光方块，58x58 */
  .mobile-menu-btn {
    display: flex !important;
    align-items: center; justify-content: center;
    width: 58px; height: 58px;
    background: rgba(21,43,74,0.6);
    border: 2px solid rgba(0,122,255,0.35);
    border-radius: 14px;
    box-shadow: inset 0 0 0 1px rgba(0,122,255,0.15), 0 0 12px rgba(0,122,255,0.2);
    color: #ffffff; cursor: pointer; transition: all .25s ease;
  }
  .mobile-menu-btn:hover {
    background: rgba(21,43,74,0.8);
    border-color: #2b84ff;
    box-shadow: inset 0 0 0 1px rgba(0,122,255,0.25), 0 0 16px rgba(0,122,255,0.35);
  }

  /* 下拉菜单容器 */
  .mobile-menu {
    position: fixed !important; top: 60px; right: 15px;
    background: rgba(26,31,46,0.98); backdrop-filter: blur(20px);
    border: 1px solid rgba(0,122,255,0.3); border-radius: 12px;
    min-width: 200px; box-shadow: 0 10px 40px rgba(0,0,0,0.4);
    z-index: 10000; display: none !important; flex-direction: column; padding: 10px 0;
  }
  .mobile-menu.show { display: flex !important; }
}
```

### 2.9 响应式设计

```css
/* 响应式断点 */
@media (max-width: 1400px) {
    .nav-menu {
        gap: 10px;
        margin-left: 20px;
    }
    
    .nav-item {
        padding: 6px 10px;
        font-size: 12px;
    }
    
    .nav-title {
        font-size: 16px;
    }
}

@media (max-width: 1200px) {
    .nav-menu {
        gap: 8px;
        margin-left: 15px;
    }
    
    .nav-item {
        padding: 5px 8px;
        font-size: 11px;
    }
}

@media (max-width: 1000px) {
    .navbar {
        padding: 0 15px;
    }
    
    .nav-title {
        display: none;
    }
    
    .nav-menu {
        margin-left: 10px;
        flex: 1;
    }
    
    .nav-user {
        max-width: none;
        min-width: 120px;
        overflow: hidden;
        text-overflow: ellipsis;
    }
    
    .nav-user span {
        max-width: 150px;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }
}

@media (max-width: 768px) {
    .nav-menu {
        display: none;
    }

    /* 移动端导航栏布局调整 */
    .navbar {
        padding: 0 15px;
        min-height: 60px;
    }

    .nav-left {
        flex: 1;
        min-width: 0;
    }

    .nav-logo {
        width: 35px;
        height: 35px;
        margin-right: 12px;
    }

    .nav-title {
        display: none;
    }
    
    /* 移动端用户信息显示切换 */
    .desktop-user {
        display: none;
    }

    .mobile-user {
        display: inline;
    }
    
    .nav-user {
        background: transparent;
        padding: 0;
        margin-right: 15px;
        border-radius: 0;
    }
    
    .nav-user span {
        max-width: 140px;
        font-size: 14px;
        color: #ffffff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
    }

    /* 移动端右侧区域布局 */
    .nav-right {
        gap: 8px;
        align-items: center;
    }

    /* 语言选择器移动端样式 */
    .language-dropdown .nav-item {
        padding: 4px 8px !important;
        font-size: 12px;
    }

    .language-dropdown .nav-item span {
        font-size: 11px !important;
    }

    .language-dropdown .nav-item img {
        width: 12px !important;
        height: 12px !important;
    }
    
    /* 移动端汉堡菜单按钮 */
    .mobile-menu-btn {
        display: flex !important;
        align-items: center;
        justify-content: center;
        width: 40px;
        height: 40px;
        background: rgba(0, 122, 255, 0.1);
        border: 1px solid rgba(0, 122, 255, 0.2);
        border-radius: 8px;
        color: #ffffff;
        cursor: pointer;
        transition: all 0.3s ease;
        margin-left: 10px;
    }
    
    .mobile-menu-btn:hover {
        background: rgba(0, 122, 255, 0.2);
        border-color: #007AFF;
    }
    
    /* 移动端下拉菜单样式 */
    .mobile-menu {
        position: fixed !important;
        top: 60px;
        right: 15px;
        background: rgba(26, 31, 46, 0.98);
        backdrop-filter: blur(20px);
        border: 1px solid rgba(0, 122, 255, 0.3);
        border-radius: 12px;
        min-width: 200px;
        box-shadow: 0 10px 40px rgba(0, 0, 0, 0.4);
        z-index: 10000;
        display: none !important;
        flex-direction: column;
        padding: 10px 0;
    }
    
    .mobile-menu.show {
        display: flex !important;
    }
    
    .mobile-menu a {
        color: rgba(255, 255, 255, 0.8);
        text-decoration: none;
        padding: 12px 20px;
        font-size: 14px;
        border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        transition: all 0.2s ease;
        display: flex;
        align-items: center;
        gap: 10px;
    }
    
    .mobile-menu a:hover {
        background: rgba(0, 122, 255, 0.15);
        color: rgba(255, 255, 255, 1);
    }
    
    .mobile-menu a:last-child {
        border-bottom: none;
    }
}
```

## 3. JavaScript功能标准

### 3.1 移动端菜单控制

```javascript
// 移动端菜单控制函数
function toggleMobileMenu() {
    const mobileMenu = document.getElementById('mobileMenu');
    if (mobileMenu.classList.contains('show')) {
        mobileMenu.classList.remove('show');
    } else {
        mobileMenu.classList.add('show');
    }
}

// 点击页面其他地方关闭移动端菜单
document.addEventListener('click', function(event) {
    const mobileMenu = document.getElementById('mobileMenu');
    const mobileMenuBtn = document.querySelector('.mobile-menu-btn');
    
    if (mobileMenu && mobileMenuBtn && 
        !mobileMenu.contains(event.target) && 
        !mobileMenuBtn.contains(event.target)) {
        mobileMenu.classList.remove('show');
    }
});
```

### 3.2 语言切换功能

```javascript
function updateaLang(obj) {
    document.getElementById("planguage").value = obj;
    $.ajax({
        type: "POST",
        url: "saveLanguage.do",
        data: $('#languageForm').serialize(),
        success: function (result) {
            location.reload();
        },
        error: function() {
            // 错误处理
        }
    });
}
```

### 3.3 隐藏表单

```html
<!-- 隐藏的语言表单 -->
<form class="card card-md" id="languageForm" method="post" style="display: none;">
    <input type="hidden" name="planguage" id="planguage"/>
</form>
```

## 4. 主题变量标准

### 4.1 颜色变量

| 变量名 | 值 | 用途 |
|--------|----|----- |
| 主色调 | `#007AFF` | 链接、按钮、强调色 |
| 成功色 | `#00FF88` | 成功状态、正数金额 |
| 警告色 | `#FF9500` | 警告状态 |
| 错误色 | `#FF3B30` | 错误状态、负数金额 |
| 背景色 | `rgba(26, 31, 46, 0.95)` | 导航栏背景 |
| 文本色 | `#ffffff` | 主要文本 |
| 次要文本色 | `rgba(255, 255, 255, 0.8)` | 次要文本 |
| 边框色 | `rgba(0, 122, 255, 0.2)` | 边框和分割线 |

### 4.2 尺寸变量

| 变量名 | 值 | 用途 |
|--------|----|----- |
| 导航栏高度 | `70px` | 顶部导航栏高度 |
| Logo尺寸 | `40px × 40px` | 系统Logo大小 |
| 图标尺寸 | `20px × 20px` | 操作按钮图标 |
| 移动端图标 | `16px × 16px` | 移动端菜单图标 |
| 圆角大小 | `8px` | 按钮和容器圆角 |
| 间距标准 | `15px` | 元素间标准间距 |

### 4.3 字体规范

| 用途 | 字体大小 | 字重 | 行高 |
|------|---------|------|------|
| 系统标题 | `18px` | `500` | `1.2` |
| 导航项 | `13px` | `400` | `1.4` |
| 用户名 | `13px` | `400` | `1.4` |
| 移动端菜单 | `14px` | `400` | `1.5` |
| 下拉菜单 | `14px` | `400` | `1.4` |

## 5. 响应式断点标准

| 断点 | 屏幕宽度 | 显示效果 | 主要变化 |
|------|---------|---------|---------|
| 大屏幕 | `> 1400px` | 完整桌面端 | 所有元素正常显示，标准间距 |
| 中大屏幕 | `1201px - 1400px` | 压缩桌面端 | 导航项间距减少，字体稍小 |
| 中屏幕 | `1001px - 1200px` | 紧凑桌面端 | 进一步压缩间距 |
| 小屏幕 | `769px - 1000px` | 隐藏标题桌面端 | 隐藏系统标题，用户名优化 |
| 移动端 | `≤ 768px` | 移动端模式 | 显示汉堡菜单，隐藏桌面导航 |

## 6. 动态内容配置

### 6.1 条件显示元素

```html
<!-- WebTrader链接 - 根据配置条件显示 -->
<a th:if="${WEB_TRADER_STATUS} eq '1'" th:href="${WEB_TRADER_URL}" target="_blank" class="nav-item" th:text="#{menu.webtrader}">WebTrader</a>

<!-- 客户支持链接 - 根据配置条件显示 -->
<a th:if="${SUPPORT_STATUS} eq '1'" th:href="${SupportCenter}" target="_blank" class="nav-item" th:text="#{menu.support}">Support</a>

<!-- 桌面端操作按钮 - 根据设备类型显示 -->
<div th:if="${isMobile==0}" style="display: flex; gap: 12px;">
    <!-- 操作按钮内容 -->
</div>
```

### 6.2 动态变量

| 变量名 | 类型 | 描述 | 必需 |
|--------|------|------|------|
| `${CRM_LOGO1}` | String | 系统Logo图片路径 | ✅ |
| `${CRM_TITLE}` | String | 系统标题 | ✅ |
| `${userInfo.userName}` | String | 当前用户名（桌面端和移动端显示） | ✅ |
| `${WEB_TRADER_STATUS}` | String | WebTrader功能状态 | ✅ |
| `${WEB_TRADER_URL}` | String | WebTrader访问地址 | ⚠️ |
| `${SUPPORT_STATUS}` | String | 客户支持功能状态 | ✅ |
| `${SupportCenter}` | String | 客户支持中心地址 | ⚠️ |
| `${isMobile}` | Integer | 设备类型标识 | ✅ |

### 6.3 移动端用户信息显示

```html
<!-- 用户信息显示 - 支持桌面端和移动端切换 -->
<div class="nav-user">
    <span class="desktop-user">[[${userInfo.userName}]]</span>
    <span class="mobile-user">[[${userInfo.userName}]]</span>
</div>
```

**说明**：
- 桌面端显示用户名
- 移动端可以显示用户名或邮箱地址
- 通过CSS控制在不同设备上的显示内容

## 7. 国际化支持

### 7.1 多语言文本标签

| 标签 | 中文 | 英文 | 描述 |
|------|------|------|------|
| `#{menu.home}` | 首页 | Home | 首页导航 |
| `#{menu.fundtransfer}` | 资金转账 | Fund Transfer | 资金转账导航 |
| `#{menu.fundrecords}` | 资金记录 | Fund Records | 资金记录导航 |
| `#{menu.traderecords}` | 交易记录 | Trade Records | 交易记录导航 |
| `#{menu.webtrader}` | 网页交易 | WebTrader | 网页交易导航 |
| `#{menu.profile}` | 个人资料 | Profile | 个人资料导航 |
| `#{menu.support}` | 客户支持 | Support | 客户支持导航 |
| `#{main.language}` | 语言 | Language | 语言选择 |
| `#{main.modifypassword}` | 修改密码 | Change Password | 修改密码功能 |
| `#{main.logout}` | 退出登录 | Logout | 退出登录功能 |

### 7.2 支持语言列表

1. 简体中文 (`zh-cn`)
2. 繁體中文 (`zh-tw`) 
3. English (`en`)
4. ภาษาไทย (`th`)
5. Bahasa Malay (`ms`)
6. Bahasa Indonesia (`id`)
7. Tiếng Việt (`vi`)
8. 日本語 (`ja`)

## 8. 代码一致性标准

### 8.1 缩进规范
- **统一使用2个空格**进行缩进
- **所有嵌套元素必须正确缩进**
- **HTML注释与代码保持相同缩进级别**

### 8.2 空行规范
```html
<!-- 正确的空行格式 -->
        <div class="nav-right">

          <!-- 用户信息和操作 -->
          <div class="nav-user">
            <span>[[${userInfo.userName}]]</span>
                </div>
                
          <!-- 语言切换下拉菜单 -->
          <div class="nav-item dropdown language-dropdown">
```

### 8.3 国际化文本规范
- **所有导航项必须使用国际化标签**
- **特别注意WebTrader链接**：`th:text="#{menu.webtrader}"`
- **条件显示的链接也要使用国际化**

### 8.4 移动端菜单缩进标准
```html
<!-- 正确的移动端菜单缩进 -->
          <div id="mobileMenu" class="mobile-menu">
            <a href="main">
              <svg width="16" height="16">...</svg>
              <span th:text="#{menu.home}">首页</span>
            </a>
            <a href="deposit">
              <svg width="16" height="16">...</svg>
              <span th:text="#{menu.fundtransfer}">资金转账</span>
            </a>
          </div>
```

### 8.5 条件显示元素格式
```html
<!-- 正确的条件显示格式 -->
<a th:if="${WEB_TRADER_STATUS} eq '1'" 
   th:href="${WEB_TRADER_URL}" 
   target="_blank" 
   class="nav-item" 
   th:text="#{menu.webtrader}">WebTrader</a>
```

## 9. 实施检查清单

### 9.1 HTML结构检查

- [ ] 导航栏使用 `.navbar` 类
- [ ] 包含完整的 `.nav-left` 和 `.nav-right` 结构
- [ ] Logo使用动态 `${CRM_LOGO1}` 变量
- [ ] 系统标题使用动态 `${CRM_TITLE}` 变量
- [ ] 用户名使用动态 `${userInfo.userName}` 变量
- [ ] 所有文本使用国际化标签
- [ ] 条件显示元素使用正确的 `th:if` 语法
- [ ] 移动端菜单包含所有导航项
- [ ] **代码缩进完全一致**（2空格标准）
- [ ] **WebTrader链接使用国际化文本** `th:text="#{menu.webtrader}"`
- [ ] **空行和注释格式统一**
- [ ] **移动端菜单项缩进统一**
- [ ] **移动端用户信息显示正确**（支持桌面端/移动端切换）
- [ ] **移动端导航栏高度和布局正确**（60px高度）
- [ ] **移动端下拉菜单位置正确**（top: 60px）

### 9.2 CSS样式检查

- [ ] 包含完整的导航栏基础样式
- [ ] 实现响应式断点设计
- [ ] 移动端菜单默认隐藏
- [ ] 大屏幕强制隐藏移动端元素
- [ ] 语言下拉菜单样式完整
- [ ] 操作按钮样式正确
- [ ] 悬停效果实现
- [ ] z-index层级管理正确

### 9.3 JavaScript功能检查

- [ ] 移动端菜单切换功能正常
- [ ] 点击外部区域关闭菜单
- [ ] 语言切换功能完整
- [ ] 隐藏表单元素存在
- [ ] 事件监听器正确绑定

### 9.4 响应式测试

- [ ] 大屏幕（>1400px）显示正常
- [ ] 中等屏幕（1200px-1400px）压缩正确
- [ ] 小屏幕（768px-1200px）适配良好
- [ ] 移动端（≤768px）汉堡菜单工作
- [ ] 用户名在各尺寸下显示正确
- [ ] 语言下拉菜单在各设备正常工作

### 9.5 浏览器兼容性

- [ ] Chrome 80+ 支持
- [ ] Firefox 75+ 支持
- [ ] Safari 13+ 支持
- [ ] Edge 80+ 支持
- [ ] 移动端 Safari 支持
- [ ] 移动端 Chrome 支持

## 10. 常见问题与解决方案

### 10.1 移动端菜单不显示
**问题**: 点击汉堡菜单按钮，下拉菜单不出现
**解决方案**: 
1. 检查JavaScript函数 `toggleMobileMenu()` 是否正确加载
2. 确认CSS中 `.mobile-menu.show` 样式存在
3. 验证z-index层级设置正确

### 10.2 导航栏在小屏幕上重叠
**问题**: 导航项在中等屏幕上互相重叠或被截断
**解决方案**:
1. 检查响应式断点CSS是否正确加载
2. 调整 `.nav-menu` 的gap值
3. 确保 `white-space: nowrap` 设置正确

### 10.3 语言下拉菜单被遮挡
**问题**: 语言选择下拉菜单显示在其他元素后面
**解决方案**:
1. 检查 `.language-dropdown` 的z-index设置
2. 确保父容器没有 `overflow: hidden`
3. 验证 `position: relative` 设置正确

### 10.4 用户名显示不完整
**问题**: 长用户名在导航栏中被截断
**解决方案**:
1. 调整 `.nav-user` 的max-width值
2. 使用 `text-overflow: ellipsis` 优雅截断
3. 考虑在悬停时显示完整用户名

### 10.5 移动端菜单点击无响应
**问题**: 移动端菜单项点击后没有跳转
**解决方案**:
1. 检查链接href属性是否正确
2. 确认没有阻止默认事件的JavaScript
3. 验证路由配置正确

## 11. 性能优化建议

### 11.1 CSS优化
- 合并重复的样式规则
- 使用CSS变量减少重复代码
- 避免过度嵌套的选择器
- 使用高效的选择器

### 11.2 JavaScript优化
- 延迟加载非关键脚本
- 避免重复绑定事件监听器
- 使用事件委托优化性能
- 缓存DOM元素引用

### 11.3 图标优化
- 使用SVG图标而非图片
- 合并常用图标到sprite
- 优化SVG代码减少体积
- 考虑使用图标字体

## 12. 无障碍性(A11y)标准

### 12.1 键盘导航支持
```css
/* 焦点状态样式 */
.nav-item:focus {
    outline: 2px solid #007AFF;
    outline-offset: 2px;
}

.mobile-menu-btn:focus {
    outline: 2px solid #007AFF;
    outline-offset: 2px;
}
```

### 12.2 屏幕阅读器支持
```html
<!-- 添加aria标签 -->
<nav class="navbar" role="navigation" aria-label="主导航">
<div class="mobile-menu-btn" 
     aria-label="打开菜单" 
     aria-expanded="false"
     role="button"
     tabindex="0">
```

### 12.3 颜色对比度
- 文字与背景对比度至少4.5:1
- 重要按钮对比度至少3:1
- 悬停状态保持足够对比度

## 13. 浏览器兼容性详细说明

### 13.1 现代浏览器全支持
- Chrome 80+: 完全支持所有特性
- Firefox 75+: 完全支持所有特性  
- Safari 13+: 完全支持所有特性
- Edge 80+: 完全支持所有特性

### 13.2 部分支持的特性
- `backdrop-filter`: IE不支持，提供降级方案
- CSS Grid: IE11需要前缀
- Flexbox gap: 老版本Safari需要margin替代

### 13.3 降级方案
```css
/* backdrop-filter降级 */
.navbar {
    background: rgba(26, 31, 46, 0.95);
    backdrop-filter: blur(20px);
    /* 降级方案 */
    background: rgba(26, 31, 46, 0.98) \9; /* IE */
}
```

## 14. 安全性考虑

### 14.1 XSS防护
- 所有用户输入必须转义
- 使用CSP头防止脚本注入
- 验证所有外部链接

### 14.2 CSRF防护
- 语言切换等POST请求使用CSRF token
- 验证来源和引用头
- 使用安全的cookie设置

## 15. 维护和更新指南

### 15.1 添加新导航项

1. **桌面端导航**
```html
<div class="nav-menu">
    <!-- 现有项目 -->
    <a href="newpage" class="nav-item" th:text="#{menu.newpage}">New Page</a>
</div>
```

2. **移动端导航**
```html
<div class="mobile-menu">
    <!-- 现有项目 -->
    <a href="newpage">
        <svg width="16" height="16"><!-- 图标 --></svg>
        <span th:text="#{menu.newpage}">新页面</span>
    </a>
</div>
```

3. **国际化配置**
```properties
# messages.properties
menu.newpage=New Page

# messages_zh_CN.properties  
menu.newpage=新页面
```

### 15.2 修改样式主题

1. **定义颜色变量**
```css
:root {
    --primary-color: #007AFF;
    --background-color: rgba(26, 31, 46, 0.95);
    --text-color: #ffffff;
}
```

2. **更新组件样式**
```css
.navbar {
    background: var(--background-color);
    color: var(--text-color);
}
```

### 15.3 响应式断点调整

```css
/* 自定义断点 */
@media (max-width: 1600px) { /* 超大屏 */ }
@media (max-width: 1400px) { /* 大屏 */ }
@media (max-width: 1200px) { /* 中屏 */ }
@media (max-width: 992px)  { /* 小屏 */ }
@media (max-width: 768px)  { /* 移动端 */ }
@media (max-width: 576px)  { /* 小移动端 */ }
```

### 15.4 版本兼容性管理

- **向后兼容**: 新版本必须兼容现有页面
- **渐进增强**: 新特性逐步推出
- **文档更新**: 每次更改都要更新文档
- **测试覆盖**: 确保所有设备和浏览器正常工作

## 16. 开发工作流

### 16.1 开发阶段
1. 复制标准HTML结构
2. 添加页面特定的导航状态
3. 测试所有响应式断点
4. 验证JavaScript功能
5. 检查无障碍性

### 16.2 代码审查清单
- [ ] HTML结构符合标准
- [ ] CSS样式完整
- [ ] JavaScript功能正常
- [ ] 响应式设计正确
- [ ] 无障碍性标准达标
- [ ] 浏览器兼容性测试通过
- [ ] 性能指标符合要求

### 16.3 部署前检查
- [ ] 所有链接正确
- [ ] 国际化文本显示正常
- [ ] 移动端菜单工作正常
- [ ] 语言切换功能正常
- [ ] 用户信息显示正确

## 17. 扩展功能

### 17.1 主题切换支持
```javascript
// 主题切换功能
function toggleTheme() {
    document.body.classList.toggle('dark-theme');
    localStorage.setItem('theme', 
        document.body.classList.contains('dark-theme') ? 'dark' : 'light'
    );
}
```

### 17.2 通知徽章
```html
<a href="notifications" class="nav-item">
    <span>通知</span>
    <span class="notification-badge">3</span>
</a>
```

### 17.3 用户头像支持
```html
<div class="nav-user">
    <img src="${userInfo.avatar}" class="user-avatar" alt="用户头像">
    <span>[[${userInfo.userName}]]</span>
</div>
```

## 18. 监控和分析

### 18.1 性能监控
- 导航栏加载时间
- 移动端菜单响应时间
- 语言切换性能
- 内存使用情况

### 18.2 用户行为分析
- 导航项点击率
- 移动端菜单使用率
- 语言切换频率
- 设备类型分布

### 18.3 错误监控
- JavaScript错误捕获
- CSS加载失败监控
- 图标加载失败处理
- 网络异常处理

---

**版本信息**: v2.0.0 (2025-01-27)
**维护者**: CRM前端开发团队
**更新频率**: 根据需求和反馈定期更新

**注意：本规范是CRM系统顶部导航栏的强制标准，所有新页面必须严格按照此规范实施。任何偏离都需要经过架构评审批准。**
