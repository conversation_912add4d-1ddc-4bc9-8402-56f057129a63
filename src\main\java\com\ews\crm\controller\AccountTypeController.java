
package com.ews.crm.controller;

import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.AccountLeverMap;
import com.ews.crm.entity.AccountType;
import com.ews.crm.service.AccountLeverMapService;
import com.ews.crm.service.AccountTypeService;



@RestController
@RequestMapping("/admin/accountType")
public class AccountTypeController {
	@Autowired
	private AccountTypeService accountTypeService;
	
	@Autowired
	private AccountLeverMapService accountLeverMapService;




   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	AccountType query  = new AccountType();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "asc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("typeName"))) {
               	query.setTypeName(request.getParameter("typeName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
               	query.setGroupName(request.getParameter("groupName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("isShow"))) {
               	query.setIsShow(Integer.parseInt(request.getParameter("isShow").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("openType"))) {
               	query.setOpenType(Integer.parseInt(request.getParameter("openType").trim()));
        	}
        	Page<AccountType> pages = accountTypeService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<AccountType> accountTypes = pages.getContent();
        	for(int i=0;i<accountTypes.size();i++) {
        		AccountType entity  = accountTypes.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", accountTypes);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			AccountType accountType = new AccountType();
        	accountType.setTypeName(data.getString("typeName"));
        	accountType.setGroupName(data.getString("groupName"));
        	accountType.setBackup1(data.getString("backup1"));
        	accountType.setBackup2(data.getString("backup2"));
        	accountType.setIsShow(data.getInteger("isShow"));
        	accountType.setOpenType(data.getInteger("openType"));
        	accountType.setCreditValue(data.getDouble("creditValue"));
        	
        	accountType.setIsAutoAudit1(data.getInteger("isAutoAudit1"));
        	accountType.setIsAutoAudit2(data.getInteger("isAutoAudit2"));
        	accountType.setMax_Accounts(data.getInteger("max_Accounts"));
        	accountType.setCountryShow(data.getString("countryShow"));
			Result re = accountTypeService.saveOrUpdate(accountType);
			
			Object[] o = data.getJSONArray("levers2").toArray();
			for(int i=0;i<o.length;i++) {
				AccountLeverMap alm=new AccountLeverMap();
				alm.setAccountTypeId(accountType.getId());
				alm.setLeverId(new Long(o[i].toString()));
				accountLeverMapService.saveOrUpdate(alm);
			}
			
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(accountType);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			AccountType accountType = this.accountTypeService.findById(id);
				if (accountType != null) {
					accountType.setTypeName(data.getString("typeName"));
					accountType.setGroupName(data.getString("groupName"));
					accountType.setIsShow(data.getInteger("isShow"));
					accountType.setOpenType(data.getInteger("openType"));
					accountType.setBackup1(data.getString("backup1"));
					accountType.setBackup2(data.getString("backup2"));
					accountType.setCreditValue(data.getDouble("creditValue"));
					
					accountType.setIsAutoAudit1(data.getInteger("isAutoAudit1"));
		        	accountType.setIsAutoAudit2(data.getInteger("isAutoAudit2"));
		        	accountType.setMax_Accounts(data.getInteger("max_Accounts"));
		        	System.out.println(data.getString("countryShow"));
		        	accountType.setCountryShow(data.getString("countryShow"));
					Result re = accountTypeService.saveOrUpdate(accountType);
					
					AccountLeverMap query=new AccountLeverMap();
					query.setAccountTypeId(accountType.getId());
					
					Page<AccountLeverMap> alm_p=this.accountLeverMapService.findAll(0,10000,"id", "asc", query);
					for(int m=0;m<alm_p.getContent().size();m++) {
						AccountLeverMap alm=(AccountLeverMap)alm_p.getContent().get(m);
						this.accountLeverMapService.removeEntityById(alm.getId());
					}
					
					Object[] o = data.getJSONArray("levers2").toArray();
					for(int i=0;i<o.length;i++) {
						AccountLeverMap alm=new AccountLeverMap();
						alm.setAccountTypeId(accountType.getId());
						alm.setLeverId(new Long(o[i].toString()));
						accountLeverMapService.saveOrUpdate(alm);
					}
					
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update error");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = accountTypeService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = accountTypeService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update error"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	@PostMapping("/listAll")
    public ResponseData listAll(HttpServletRequest request) {
    	try {
        	AccountType query  = new AccountType();
        	query.setIsShow(1);
        	Page<AccountType> pages = accountTypeService.findAll(0, 100, "id", "asc", query);
        	JSONObject datas = new JSONObject();
        	List<AccountType> accountTypes = pages.getContent();
        	return ResponseDataUtil.buildSuccess(accountTypes);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


}

