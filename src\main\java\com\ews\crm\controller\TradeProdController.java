
package com.ews.crm.controller;

import java.net.URI;
import java.util.Date;
import java.util.HashMap;
import java.util.List;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.common.MyWebsocketClient4TradeProd;
import com.ews.common.Result;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeProd;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeProdService;



@RestController
@RequestMapping("/admin/tradeProd")
public class TradeProdController {
	@Autowired
	private TradeProdService tradeProdService;


	@Autowired
	private ServerSettingService serverSettingService;

   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	TradeProd query  = new TradeProd();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("prodName"))) {
               	query.setProdName(request.getParameter("prodName").trim());
        	}
        	Page<TradeProd> pages = tradeProdService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<TradeProd> tradeProds = pages.getContent();
        	for(int i=0;i<tradeProds.size();i++) {
        		TradeProd entity  = tradeProds.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", tradeProds);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			TradeProd tradeProd = new TradeProd();
        	tradeProd.setProdName(data.getString("prodName"));
        	tradeProd.setProdDescribe(data.getString("prodDescribe"));
			Result re = tradeProdService.saveOrUpdate(tradeProd);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(tradeProd);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			TradeProd tradeProd = this.tradeProdService.findById(id);
				if (tradeProd != null) {
					tradeProd.setProdName(data.getString("prodName"));
					tradeProd.setProdDescribe(data.getString("prodDescribe"));
					Result re = tradeProdService.saveOrUpdate(tradeProd);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = tradeProdService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = tradeProdService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	@GetMapping("/synchroProd")
	public ResponseData synchroProd( HttpServletRequest request) {
		 try {
				
				ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
				MyWebsocketClient4TradeProd client= new MyWebsocketClient4TradeProd(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
				client.setTradeProdService(this.tradeProdService);
				client.connect();
				while (!client.getReadyState().equals(READYSTATE.OPEN)) {
					Thread.sleep(100);
					System.out.println("synchroProd");
				}
					HashMap map=new HashMap();
					map.put("reqtype", "getsysmbollistinfo");
					map.put("reqid", String.valueOf(new Date().getTime()));
				    JSONObject jsonObj=new JSONObject(map);
				    client.send(jsonObj.toString());
				}catch(Exception e) {
					System.out.println(e);
				}
		    return ResponseDataUtil.buildSuccess(); 
				
	}
	
	
	  @PostMapping("/listshow")
	    public ResponseData listshow(HttpServletRequest request) {
	    	try {
	        	TradeProd query  = new TradeProd();
	        	Page<TradeProd> pages = tradeProdService.findAll(0, 1000, "id", "asc", query);
	        	List<TradeProd> tradeProds = pages.getContent();
	        	JSONObject datas = new JSONObject();
	        	datas.put("items", tradeProds);
	        	return ResponseDataUtil.buildSuccess(datas);
	    	} catch (Exception e) {
	        	e.printStackTrace();
	        	return ResponseDataUtil.buildError(e.getMessage());
	    	}
		}


}

