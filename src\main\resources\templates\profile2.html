<!doctype html>
<html lang="en">
  <head>
    <meta charset="utf-8"/>
    <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
    <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
    <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
     <title>[[${CRM_TITLE}]]</title>
    <!-- CSS files -->
    <link href="../dist/css/tabler.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-flags.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-payments.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/tabler-vendors.min.css?1684106062" rel="stylesheet"/>
    <link href="../dist/css/demo.min.css?1684106062" rel="stylesheet"/>
	 <script src="js/jquery.min.js"></script>
      <script src="js/layer/layer.js"></script>
<link rel="shortcut icon" th:href="${CRM_LOGO2}">
<script async th:src="@{'https://www.googletagmanager.com/gtag/js?id='+${JS_ID1}}" th:if="${HEADER_SCRIPT_STATUS}==1"></script>
<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1' ">
  window.dataLayer = window.dataLayer || [];
  function gtag(){dataLayer.push(arguments);}
  gtag('js', new Date());

  gtag('config', [[${JS_ID1}]]);
</script>

<script  th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">
!function(f,b,e,v,n,t,s)
{if(f.fbq)return;n=f.fbq=function(){n.callMethod?
n.callMethod.apply(n,arguments):n.queue.push(arguments)};
if(!f._fbq)f._fbq=n;n.push=n;n.loaded=!0;n.version='2.0';
n.queue=[];t=b.createElement(e);t.async=!0;
t.src=v;s=b.getElementsByTagName(e)[0];
s.parentNode.insertBefore(t,s)}(window, document,'script',
'https://connect.facebook.net/en_US/fbevents.js');
fbq('init', [[${JS_ID2}]]);
fbq('track', 'PageView');
</script>
<noscript>
<img th:if="${HEADER_SCRIPT_STATUS} eq '1'"  height="1" width="1" style="display:none"
th:src="@{'https://www.facebook.com/tr?id='+${JS_ID2}+'&ev=PageView&noscript=1'}"/>
</noscript>

<!-- Google Tag Manager -->
<script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],
j=d.createElement(s),dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src=
'https://www.googletagmanager.com/gtm.js?id='+i+dl;f.parentNode.insertBefore(j,f);
})(window,document,'script','dataLayer',[[${JS_ID3}]]);</script>
<!-- End Google Tag Manager -->
    <style>
      @import url('https://rsms.me/inter/inter.css');
      :root {
      	--tblr-font-sans-serif: 'Inter Var', -apple-system, BlinkMacSystemFont, San Francisco, Segoe UI, Roboto, Helvetica Neue, sans-serif;
      }
      body {
      	font-feature-settings: "cv03", "cv04", "cv11";
      }
    </style>
      <style>
      @media screen and (min-width: 1080px) {
      .lest {
        width: 100%;
        height: 50px;
        display: flex;
        justify-content: center;
        position: fixed;
        bottom: 0;
        left: 0;
        right: 0;
      }

      .conter {
        padding: 0px 20px;
        width: 85vw;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content:space-between;
        align-items: center;
      }

      .second {
        display: flex;
        margin-right: 10px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 15px;
        display: flex;
        align-items: center;
      }

      .button a {
        color: #fff;
        font-size: 10px;
      }

      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }

    @media screen and (max-width: 1080px) {
      .lest {
        width: 100%;
        display: flex;
        justify-content: center;
        /* position: fixed;
        bottom: 0;
        left: 0;
        right: 0; */
      }

      .conter {
        padding: 0px 20px;
        width: 80%;
        background-color: #000000;
        opacity: 0.66;
        border-top-left-radius: 10px;
        border-top-right-radius: 10px;
        display: flex;
        justify-content: flex-start;
        align-items: center;
      }

      .second {
        width: 100%;
        padding: 10px 0px;
      }

      .button {
        border: 1px solid #fff;
        border-radius: 5px;
        margin-right: 10px;
        padding: 0px 20px;
      }

      .button a {
        color: #fff;
        overflow: hidden;
        text-overflow: ellipsis;
        white-space: nowrap;
      }
      .button img{
        width: 30px;
      }

      .button .winimg{
        width: 15px;
        padding-right: 5px;
      }
    }
    </style>
  </head>
  <body >
    <script src="./dist/js/demo-theme.min.js?1684106062"></script>
    <div class="page">
      <!-- Navbar -->
      <header class="navbar navbar-expand-md d-print-none" >
        <div class="container-xl" >
          <button class="navbar-toggler" type="button" data-bs-toggle="collapse" data-bs-target="#navbar-menu" aria-controls="navbar-menu" aria-expanded="false" aria-label="Toggle navigation">
            <span class="navbar-toggler-icon"></span>
          </button>
          <h1 class="navbar-brand navbar-brand-autodark d-none-navbar-horizontal pe-0 pe-md-3">
            <a href="main">
              <img th:src="${CRM_LOGO1}" width="110" height="32" alt="Tabler" class="navbar-brand-image">
            </a>
          </h1>
		   <div class="collapse navbar-collapse" id="navbar-menu" >
          <div  >
            <div class="container-xl" >
              <ul class="navbar-nav"  >
                <li class="nav-item ">
                  <a class="nav-link" href="main" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/home -->
                      <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M5 12l-2 0l9 -9l9 9l-2 0" /><path d="M5 12v7a2 2 0 0 0 2 2h10a2 2 0 0 0 2 -2v-7" /><path d="M9 21v-6a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v6" /></svg>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.home}">
                      Home
                    </span>
                  </a>
                </li>
                <li class="nav-item ">
                  <a class="nav-link" href="fundTransfer" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/package -->
                     <img src="../img/Funds.png" width="18" height="18"/>
                    </span>
					 <span class="nav-link-title"   th:text="#{menu.fundtransfer}">
                      Fund Transfer
                    </span>
                   
                  </a>
                  
                </li>
                <li class="nav-item" >
                  <a class="nav-link" href="fundList"   >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title" th:text="#{menu.fundrecords}">
                     资金记录
                    </span>
                  </a>
                </li>
                <li class="nav-item" >
                  <a class="nav-link" href="orderList"   >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title" th:text="#{menu.traderecords}">
                     交易记录
                    </span>
                  </a>
                </li>
              <li class="nav-item" th:if="${WEB_TRADER_STATUS}  eq '1' ">
                  <a class="nav-link" th:href="${WEB_TRADER_URL}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                       <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M4 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M4 13m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 4m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v3a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /><path d="M14 15m0 2a2 2 0 0 1 2 -2h2a2 2 0 0 1 2 2v1a2 2 0 0 1 -2 2h-2a2 2 0 0 1 -2 -2z" /></svg>
                    </span>
                    <span class="nav-link-title"   >
                     WebTrader
                    </span>
                  </a>
                </li>
               
               
				 <li class="nav-item active">
                  <a class="nav-link" href="profile">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/star -->
                    <img src="../img/Profile.png" width="18" height="18"/>
                    </span>
                   <span class="nav-link-title"   th:text="#{menu.profile}">
                      Profile 
                    </span>
                  </a>
                </li>
                 <li class="nav-item" th:if="${SUPPORT_STATUS}  eq '1'">
                  <a class="nav-link" th:href="${SupportCenter}" target="_blank" >
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                     <img src="../img/CustomerService.png" width="18" height="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.support}">
                     Support
                    </span>
                  </a>
                </li>
              
              
              <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="changeLanguage">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Langauge.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.language}">
                     Preferred Language
                    </span>
                  </a>
                </li>
                
                 <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="modifyPwd">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/ChangePassword.png" width="18"/>
                    </span>
                    <span class="nav-link-title"  th:text="#{menu.modifypassword}">
                     Modify Password
                    </span>
                  </a>
                </li>
             
               <li class="nav-item" th:if="${isMobile==1}">
                  <a class="nav-link" href="logout">
                    <span class="nav-link-icon d-md-none d-lg-inline-block"><!-- Download SVG icon from http://tabler-icons.io/i/lifebuoy -->
                    <img src="../img/Logout.png" width="18"/>
                    </span>
                    <span class="nav-link-title"   th:text="#{menu.logout}">
                    Logout
                    </span>
                  </a>
                </li>
               
               
              </ul>
            
            </div>
          </div>
        </div>
          <div class="navbar-nav flex-row order-md-last">
          
          
             <div class="nav-item d-none d-md-flex me-3">
              <div class="btn-list">
                 [[${userInfo.userName}]]
              </div>
            </div>
            
            <div class="nav-item dropdown">
              <a href="#" class="nav-link d-flex lh-1 text-reset p-0" data-bs-toggle="dropdown" aria-label="Open user menu">
              
                <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"><img src="../img/Langauge.png" width="20"/></div>
                </div>
              </a>
              <div class="dropdown-menu dropdown-menu-end dropdown-menu-arrow">
              <a href="javascript:updateaLang('zh-cn');" class="dropdown-item"  >简体中文</a>
              <a href="javascript:updateaLang('zh-tw');" class="dropdown-item"  >繁體中文</a>
              <a href="javascript:updateaLang('en');" class="dropdown-item"  >English</a>
              <a href="javascript:updateaLang('th');" class="dropdown-item"  >ภาษาไทย</a>
              <a href="javascript:updateaLang('ms');" class="dropdown-item"  >Bahasa Malay</a>
              <a href="javascript:updateaLang('id');" class="dropdown-item"  >Bahasa Indonesia</a>
              <a href="javascript:updateaLang('vi');" class="dropdown-item" >Tiếng Việt</a>
               <a href="javascript:updateaLang('ja');" class="dropdown-item" >日本語</a>
              </div>
            </div>
            <div class="nav-item d-none d-md-flex me-3">
             <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="modifyPwd" ><img src="../img/ChangePassword.png" width="22"/></a></div>
                </div>
            </div>
             <div class="nav-item d-none d-md-flex me-3">
               <div class="d-none d-xl-block ps-2">
                  <div class="mt-1 small text-muted"> <a href="logout" ><img src="../img/Logout.png" width="20"/></a></div>
                </div>
            </div>
            
            
            
          </div>
        </div>
      </header>

      <div class="page-wrapper">
        <!-- Page header -->
        <div class="page-header d-print-none">
          <div class="container-xl">
            <div class="row g-2 align-items-center">
              <div class="col">
                <!-- Page pre-title -->
              <div class="page-pretitle"  th:text="#{profile.title}">
                  Profile
                </div>
                <h2 class="page-title"  th:text="#{profile.accountsettings}">
                  Account Settings
                </h2>
              </div>
              
            </div>
          </div>
        </div>
      
        
        
        <!-- body begin -->
        
        <div class="page-body">
        <form id="userForm" method="post">
          <div class="container-xl">
            <div class="card">
              <div class="row g-0">
               
                <div class="col d-flex flex-column">
                  <div class="card-body">
                    <h2 class="mb-4">Base  Information &nbsp;&nbsp;<small>
                    <font color="#ff9933" th:if="${userInfo.isAvailable == 0}">[[#{profile.unverified}]]</font>
                    <font color="green" th:if="${userInfo.isAvailable == 1}">[[#{profile.verified}]]</font>
                    <font color="blue" th:if="${userInfo.isAvailable == 2}">[[#{profile.autoverifying}]]</font>
                    <font color="blue" th:if="${userInfo.isAvailable == 3}">[[#{profile.manualreview}]]</font>
                    <font color="red" th:if="${userInfo.isAvailable == 4}">[[#{profile.reject}]]</font>
                   
                    </small></h2>
                     <div class="row g-3">
                    <div class="col-md">
                     <div class="form-label"  th:text="#{profile.crmaccount}">Crm Account</div>
                        [[${userInfo.userName}]]
                      </div>
                      </div>
                    <div class="row g-3">
                    
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.firstname}">First Name<font color="red">*</font></div>
                        <input type="text" class="form-control"   id="name" name="name" th:value="${userInfo.name}">
                      </div>
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.lastname}">Last Name<font color="red">*</font></div>
                        <input type="text" class="form-control"  id="surname" name="surname"  th:value="${userInfo.surname}">
                      </div>
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.dob}">DOB<font color="red">*</font></div>
                     
                      <input type="text" name="birthday" id="birthday" class="form-control mb-2" data-mask="0000-00-00" data-mask-visible="true"  autocomplete="off" th:value="${userInfo.birthday}"/>
                      </div>
                       
                    </div>
                    <div class="row g-3">
                    <div class="col-md">
                        <div class="form-label"  th:text="#{profile.citizenship}">Citizenship</div>
                        <!-- input type="text" class="form-control" id="province" name="province" th:value="${userInfo.province}"-->
                          <select class="form-select" name="province" id="province"  onChange="pdgjhjzd()" >
                          <option value=""> </option>
                                 <option  th:each="column,columnStat:${countries}"  th:selected="${column.countryCodeThree eq userInfo.province}" th:value="${column.countryCodeThree}">[[${column.countryName}]]</option>
                          </select>
                      </div>
                       <div class="col-md">
                        <div class="form-label"  th:text="#{profile.countryofresidence}">Country of residence</div>
                        <!--input type="text" class="form-control"  id="country" name="country"  th:value="${userInfo.country}"-->
                         <select class="form-select" name="country" id="country" onChange="pdgjhjzd()" >
                         <option value=""> </option>
                                 <option  th:each="column,columnStat:${countries}" th:selected="${column.countryCodeThree eq userInfo.country}"  th:value="${column.countryCodeThree}">[[${column.countryName}]]</option>
                          </select>
                      </div>
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.xxxx}">Identification</div>
                        <input type="text" class="form-control" id="identityNum" name="identityNum" th:value="${userInfo.identityNum}">
                      </div>
                      
                      
                    </div>
                  
                  <br/>
                   <h2 class="mb-4">Personal Information</h2>
                      <div class="row g-3">
                      
                     
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.city}">银行/支行名称</div>
                        <input type="text" class="form-control"  id="city" name="city"  th:value="${userInfo.city}">
                      </div>
                       <div class="col-md">
                        <div class="form-label"  th:text="#{profile.mobile}">Mobile Number</div>
                        <input type="text" class="form-control" id="tel" name="tel"  th:value="${userInfo.tel}">
                      </div>
                    </div>
                     <div class="row g-3">
                      <div class="col-md">
                        <div class="form-label"  th:text="#{profile.address}">银行账号</div>
                        <input type="text" class="form-control" id="adress" name="adress"  th:value="${userInfo.adress}">
                      </div>
                      
                    </div>
                     <br/>
                     <h2 class="mb-4"  th:text="#{profile.uploaddocuments}"  id="xxxxx2">Upload Documents</h2>
                   <div class="row g-3" >
                      <div class="col-md" >
                        <div class="form-label"  th:text="#{profile.personalidentification}">身份证</div>
                         <div id="imgsss"><img th:if="${userInfo.imageFront!=null}" th:src="${userInfo.imageFront}" height="60px;"/></div>
                         <input type="hidden"  id="imageFront" name="imageFront" th:value="${userInfo.imageFront}"  >
                         <input type="file" name="picFieldName" id="picFieldId" onchange="uploadPic(this);" style="display:none;" />
                         <button type="button" class="btn btn-primary" onclick="toUpload();"    th:text="#{profile.upload}" id="button1">upload</button>
                      </div>
                      <div class="col-md"  id="poa">
                        <div class="form-label"  th:text="#{profile.proofofaddress}">银行卡</div>
                         <div class="col-md-6" id="imgsss2"><img th:if="${userInfo.backup3!=null}" th:src="${userInfo.backup3}" height="60px;"/></div>
                        <input type="hidden"  id="backup3" name="backup3" th:value="${userInfo.backup3}">
                         <input type="file" name="picFieldName2" id="picFieldId2" onchange="uploadPic2(this);" style="display:none;" />
                         <div class="col-md-3">
                          <button type="button" class="btn btn-primary" onclick="toUpload2();"   th:text="#{profile.upload}"  id="button2">upload</button>
                        </div>
                      </div>
                      <div class="col-md" id="bst">
                        <div class="form-label"  th:text="#{profile.bankstatement}">支付宝/微信收款码</div>
                         <div class="col-md-6" id="imgsss1"><img th:if="${userInfo.imageBack!=null}" th:src="${userInfo.imageBack}" height="60px;"/></div>
                         <input type="hidden"  id="imageBack" name="imageBack" th:value="${userInfo.imageBack}">
                         <input type="file" name="picFieldName1" id="picFieldId1" onchange="uploadPic1(this);" style="display:none;" />
                         <div class="col-md-3">
                          <button type="button" class="btn btn-primary" onclick="toUpload1();"   th:text="#{profile.upload}"  id="button3">upload</button>
                        </div>
                      </div>
                       
                    </div>
                    
                    
                 
                     
                     
                  </div>
                  <div class="card-footer bg-transparent mt-auto">
                    <div class="btn-list justify-content-end">
                       <!-- button type="button" class="btn btn-primary " id="cavbutton" onclick="tocva();"  th:if="${userInfo.isAvailable == 2}"  th:text="#{profile.continueautoverify}">Continue Auto Verify</button-->
                     
                       
                       <!-- button type="button" class="btn btn-secondary disabled" id="tjbutton2"   th:if="${userInfo.isAvailable != 1}"  >Modify</button-->
                       <!-- button type="button" class="btn btn-primary " id="tjbutton3"  onclick="toUpdate();"  th:if="${userInfo.isAvailable == 1}"  >Modify</button-->
                       
                       <button type="button" class="btn btn-primary" id="tjbutton4" onclick="toSave();"  th:if="${userInfo.isAvailable == 0||userInfo.isAvailable == 4}"  th:text="#{profile.submit}">Submit</button>
                       <button type="button" class="btn btn-secondary disabled" id="tjbutton"   th:if="${userInfo.isAvailable != 0&&userInfo.isAvailable != 4}"  th:text="#{profile.submit}">Submit</button>
                
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </div>
          </form>
        </div>
     
        <!-- body end -->
        
        
        
        
      
      
      
        <footer class="footer footer-transparent d-print-none">
          <div class="container-xl">
            <div class="row text-center align-items-center flex-row-reverse">
              <div class="col-lg-auto ms-lg-auto">
                <ul class="list-inline list-inline-dots mb-0">
                
                </ul>
              </div>
              <div class="col-12 col-lg-auto mt-3 mt-lg-0">
                <ul class="list-inline list-inline-dots mb-0">
				<center>
                  <li class="list-inline-item">
                    ©Copyright 2025  All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
                  </li>
				  </center>
                 
                </ul>
              </div>
            </div>
          </div>
        </footer>
      </div>
    </div>
    <br/><br/>
      <div class="lest">
          <div class="conter">
            <div class="second" th:if="${isMobile==0}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Desktop MT4</div>
              <div class="button" style="margin-top:5px;"><img class="winimg" src="../img/win.png" alt=""><a th:href="${APP_URL1}">Windows</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a th:href="${APP_URL2}">MacOS</a></div>
            </div>
            <div class="second">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">Mobile MT4</div>
              <div class="button" style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL3}"  >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL4}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL5}">APK</a></div>
            </div>
            <div class="second" th:if="${APP3_STATUS eq '1'}">
              <div style="margin-right: 10px;color: #fff;font-size: 12px;margin-top:5px;">70 Knows Mobile</div>
              <div class="button"   style="margin-top:5px;"><img src="../img/google.png" alt=""><a  th:href="${APP_URL6}" >Google</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/ios.png" alt=""><a  th:href="${APP_URL7}">IOS</a></div>
              <div class="button" style="margin-top:5px;"><img src="../img/apk.png" alt=""><a  th:href="${APP_URL8}">APK</a></div>
            </div>
          </div>
        </div>
     <form class="card card-md" id="languageForm"  method="post">
                <input type="hidden" name="planguage" id="planguage"/>
        </form>
    <!-- Libs JS -->
    <script src="../dist/libs/apexcharts/dist/apexcharts.min.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/js/jsvectormap.min.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world.js?1684106062" defer></script>
    <script src="../dist/libs/jsvectormap/dist/maps/world-merc.js?1684106062" defer></script>
      <script src="../dist/libs/nouislider/dist/nouislider.min.js?1684106062" defer></script>
  <script src="../dist/libs/litepicker/dist/litepicker.js?1684106062" defer></script>
  <script src="../dist/libs/tom-select/dist/js/tom-select.base.min.js?1684106062" defer></script>
    <!-- Tabler Core -->
    <script src="../dist/js/tabler.min.js?1684106062" defer></script>
    <script src="../dist/js/demo.min.js?1684106062" defer></script>
     <script src="https://gosspublic.alicdn.com/aliyun-oss-sdk-4.4.4.min.js"></script>
      <script>
    // @formatter:off
    document.addEventListener("DOMContentLoaded", function () {
    	window.Litepicker && (new Litepicker({
    		element: document.getElementById('datepicker-default'),
    		buttonText: {
    			previousMonth: `<!-- Download SVG icon from http://tabler-icons.io/i/chevron-left -->
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M15 6l-6 6l6 6" /></svg>`,
    			nextMonth: `<!-- Download SVG icon from http://tabler-icons.io/i/chevron-right -->
    <svg xmlns="http://www.w3.org/2000/svg" class="icon" width="24" height="24" viewBox="0 0 24 24" stroke-width="2" stroke="currentColor" fill="none" stroke-linecap="round" stroke-linejoin="round"><path stroke="none" d="M0 0h24v24H0z" fill="none"/><path d="M9 6l6 6l-6 6" /></svg>`,
    		},
    	}));
    });
    // @formatter:on
  </script>
  
       <script>
     // @formatter:off
     document.addEventListener("DOMContentLoaded", function () {
     	var el;
     	// 初始化 province 下拉框
     	if(document.getElementById('province')) {
     		window.TomSelect && (new TomSelect(el = document.getElementById('province'), {
     			copyClassesToDropdown: false,
     			dropdownParent: 'body',
     			controlInput: '<input>',
     			render:{
     				item: function(data,escape) {
     				if( data.customProperties ){
     					return '<div><span class="dropdown-item-indicator">' + data.customProperties + '</span>' + escape(data.text) + '</div>';
     				}
     				return '<div>' + escape(data.text) + '</div>';
     			},
     			option: function(data,escape){
     				if( data.customProperties ){
     					return '<div><span class="dropdown-item-indicator">' + data.customProperties + '</span>' + escape(data.text) + '</div>';
     				}
     				return '<div>' + escape(data.text) + '</div>';
     			},
     		},
     	}));
     	}
     });
       
     document.addEventListener("DOMContentLoaded", function () {
     	var el;
     	// 调试信息
     	console.log('Country element:', document.getElementById('country'));
     	console.log('Province element:', document.getElementById('province'));
     	
     	     // 初始化 country 下拉框
     	if(document.getElementById('country')) {
     		window.TomSelect && (new TomSelect(el = document.getElementById('country'), {
     			copyClassesToDropdown: false,
     			dropdownParent: 'body',
     			controlInput: '<input>',
     			render:{
     				item: function(data,escape) {
     					if( data.customProperties ){
     					return '<div><span class="dropdown-item-indicator">' + data.customProperties + '</span>' + escape(data.text) + '</div>';
     				}
     					return '<div>' + escape(data.text) + '</div>';
     			},
     				option: function(data,escape){
     					if( data.customProperties ){
     					return '<div><span class="dropdown-item-indicator">' + data.customProperties + '</span>' + escape(data.text) + '</div>';
     				}
     					return '<div>' + escape(data.text) + '</div>';
     			},
     		},
     	}));
     	}
     	
     	// 延迟调用调试函数，确保页面完全加载
     	setTimeout(function() {
     		debugDropdowns();
     	}, 1000);
     	
     	// 添加手动刷新按钮（仅用于调试）
     	if(document.getElementById('country') && document.getElementById('country').options.length <= 1) {
     		console.warn('Country dropdown has no data, adding refresh button');
     		var refreshBtn = document.createElement('button');
     		refreshBtn.type = 'button';
     		refreshBtn.className = 'btn btn-sm btn-warning';
     		refreshBtn.textContent = 'Refresh Country Data';
     		refreshBtn.onclick = function() {
     			location.reload();
     		};
     		document.getElementById('country').parentNode.appendChild(refreshBtn);
     	}
     });
       
     // @formatter:on
   </script>
   
   
     <script th:inline="javascript">
     

   
   
       var client = new OSS.Wrapper({
        region : 'oss-ap-southeast-1',
        accessKeyId : 'LTAI4FwEEkfVCT3DnY1jh6cY',
        accessKeySecret : '******************************',
        secure: true,
        bucket : '70b'
     });
       
       function uploadPic(obj){
           var file=obj.files[0];//获取文件流
           var val= obj.value;
           var suffix = val.substr(val.indexOf("."));
           var storeAs = "/"+timestamp()+suffix;
           client.multipartUpload(storeAs, file).then(function (result) {
           	
           	if(result.res.requestUrls[0].indexOf('?') != -1){
                   this.url = result.res.requestUrls[0].split('?')[0];
                   document.getElementById("imageFront").value=url;
                   document.getElementById("imgsss").innerHTML="<img src='"+url+"' height='60px;'/>";
               }else{
                   //console.log('图片100k以内')
                   this.url = result.res.requestUrls[0];
                   document.getElementById("imageFront").value=url;
                   document.getElementById("imgsss").innerHTML="<img src='"+url+"' height='60px;'/>";
               }
           	
        
           }).catch(function (err) {
           	
           	alert(err);
              
           });
       }
       
       
    function uploadPic1(obj){
        var file=obj.files[0];//获取文件流
        var val= obj.value;
        var suffix = val.substr(val.indexOf("."));
        var storeAs = "everwings-oss/"+timestamp()+suffix;
        client.multipartUpload(storeAs, file).then(function (result) {
        	
        	if(result.res.requestUrls[0].indexOf('?') != -1){
                this.url = result.res.requestUrls[0].split('?')[0];
                document.getElementById("imageBack").value=url;
                document.getElementById("imgsss1").innerHTML="<img src='"+url+"' height='60px;'/>";
            }else{
                //console.log('图片100k以内')
                this.url = result.res.requestUrls[0];
                document.getElementById("imageBack").value=url;
                document.getElementById("imgsss1").innerHTML="<img src='"+url+"' height='60px;'/>";
            }
        	
     
        }).catch(function (err) {
        	
        	alert(err);
           
        });
    }
    function uploadPic2(obj){
        var file=obj.files[0];//获取文件流
        var val= obj.value;
        var suffix = val.substr(val.indexOf("."));
        var storeAs = "everwings-oss/"+timestamp()+suffix;
        client.multipartUpload(storeAs, file).then(function (result) {
        	
        	if(result.res.requestUrls[0].indexOf('?') != -1){
                this.url = result.res.requestUrls[0].split('?')[0];
                document.getElementById("backup3").value=url;
                document.getElementById("imgsss2").innerHTML="<img src='"+url+"' height='60px;'/>";
            }else{
                //console.log('图片100k以内')
                this.url = result.res.requestUrls[0];
                document.getElementById("backup3").value=url;
                document.getElementById("imgsss2").innerHTML="<img src='"+url+"' height='60px;'/>";
            }
        }).catch(function (err) {
           
        });
    }
    /**
     * 生成文件名
     * @returns
     */
    function timestamp(){
        var time = new Date();
        var y = time.getFullYear();
        var m = time.getMonth()+1;
        var d = time.getDate();
        var h = time.getHours();
        var mm = time.getMinutes();
        var s = time.getSeconds();
        return ""+y+add0(m)+add0(d)+add0(h)+add0(mm)+add0(s)+add0(time.getTime());
    }
    function add0(m){
        return m<10?'0'+m : m;
    }
    
    function toUpload1(){
    	　document.getElementById("picFieldId1").click(); 
    }
    function toUpload2(){
   	　document.getElementById("picFieldId2").click(); 
   }
    function toUpload(){
      	　document.getElementById("picFieldId").click(); 
      }
    
    function toSave(){
    	
    	var bl="";
    	
    	
    	if(document.getElementById("country").value==document.getElementById("province").value)
		{
		
    		 if(document.getElementById("imageFront").value!=""){
    			 bl="1";
    		 }
		
		}else{
			 if(document.getElementById("imageFront").value!=""&&document.getElementById("imageBack").value!=""&&document.getElementById("backup3").value!=""){
    			 bl="1";
    		 }
			
		}
    	
    	
    	
    	
    	if(document.getElementById("name").value==""||document.getElementById("surname").value==""||document.getElementById("birthday").value==""||document.getElementById("identityNum").value==""||bl==""){
    		
    		layer.open({
				style : 'margin-bottom : 70%;',
				content : '[[#{profile.prompt1}]]',
				skin : 'msg',
				time :5000,btn:['OK']
	    		   ,title: 'INFO',
				end:function(){
					
				}
			});
    		
    	}else{
    		$.ajax({
    			//几个参数需要注意一下
    			type: "POST",//方法类型
    			url: "saveProfile.do" ,//url
    			data: $('#userForm').serialize(),
    			success: function (result) {
    				if("true"==result) {
    					window.open("kycverified");
    					layer.open({
    						style : 'margin-bottom : 70%;',
    						content : '[[#{profile.prompt2}]]',
    						skin : 'msg',
    						time :5000,btn:['OK']
    			    		   ,title: 'INFO',
    						end:function(){
    							location.href="profile";
    						}
    					});
    					
    				}else if("none"==result) {
    					layer.open({
    						style : 'margin-bottom : 70%;',
    						content : '[[#{profile.prompt2}]]',
    						skin : 'msg',
    						time :5000,btn:['OK']
    			    		   ,title: 'INFO',
    						end:function(){
    							location.href="profile";
    						}
    					});
    					
    				}else{
    					layer.open({
    						style : 'margin-bottom : 70%;',
    						content : 'Failed',
    						skin : 'msg',
    						time : 5000,btn:['OK']
    			    		   ,title: 'INFO',
    						end:function(){
    							$("#tjbutton").attr('disabled',false);
    						}
    					});
    				}
    			},
    			error : function() {
    				layer.open({
    					style : 'margin-bottom : 70%;',
    					content : 'Error',
    					skin : 'msg',
    					time : 5000,btn:['OK']
    		    		   ,title: 'INFO',
    					end:function(){
    						$("#tjbutton").attr('disabled',false);
    					}
    				});
    			}
    		});
    		
    	}
    	
    	
    	
    }
    
    
    function pdgjhjzd(){
    	/*
    	if(document.getElementById("country") && document.getElementById("province")) {
        
    		if(document.getElementById("country").value==document.getElementById("province").value)
    		{
    			if(document.getElementById("poa")!=null){
    				document.getElementById("poa").style.display="none";
    			}
    			
    			if(document.getElementById("bst")!=null){
    				document.getElementById("bst").style.display="none";
    			}
    		}else{
    			if(document.getElementById("poa")!=null){
    				document.getElementById("poa").style.display="block";
    			}
    			if(document.getElementById("bst")!=null){
    				document.getElementById("bst").style.display="block";
    			}
    		}
    	}
        */
    }
    
    // 调试函数：检查下拉框数据
    function debugDropdowns() {
    	console.log('=== 下拉框调试信息 ===');
    	console.log('Province element:', document.getElementById('province'));
    	console.log('Country element:', document.getElementById('country'));
    	
    	if(document.getElementById('province')) {
    		console.log('Province options count:', document.getElementById('province').options.length);
    		console.log('Province selected value:', document.getElementById('province').value);
    		// 遍历所有选项
    		for(var i = 0; i < document.getElementById('province').options.length; i++) {
    			console.log('Province option ' + i + ':', document.getElementById('province').options[i].text, document.getElementById('province').options[i].value);
    		}
    	}
    	
    	if(document.getElementById('country')) {
    		console.log('Country options count:', document.getElementById('country').options.length);
    		console.log('Country selected value:', document.getElementById('country').value);
    		// 遍历所有选项
    		for(var i = 0; i < document.getElementById('country').options.length; i++) {
    			console.log('Country option ' + i + ':', document.getElementById('country').options[i].text, document.getElementById('country').options[i].value);
    		}
    	}
    	
    	// 检查 Thymeleaf 数据
    	console.log('Thymeleaf countries data:', /*[[${countries}]]*/ []);
    }
    
    pdgjhjzd();
    
    function  tocva(){
    	//window.open("kycverified");
    	
    }
    
    
    var zt="[[${userInfo.isAvailable}]]";
    if(zt=="3"){
    	 document.getElementById('name').setAttribute("readOnly", true);
    	 document.getElementById('surname').setAttribute("readOnly", true);
    	 document.getElementById('birthday').setAttribute("readOnly", true);
    	 document.getElementById('province').setAttribute("disabled", true);
    	 document.getElementById('country').setAttribute("disabled", true);
    	 
    	 document.getElementById('identityNum').setAttribute("readOnly", true);
    	 document.getElementById('city').setAttribute("readOnly", true);
    	 document.getElementById('tel').setAttribute("readOnly", true);
    	 document.getElementById('adress').setAttribute("readOnly", true);
    	 if( document.getElementById('button1')!=null){
     		document.getElementById('button1').setAttribute("disabled", true); 
     	 }
     	if( document.getElementById('button2')!=null){
     	 document.getElementById('button2').setAttribute("disabled", true);
    	 }
     	if( document.getElementById('button3')!=null){
     	 document.getElementById('button3').setAttribute("disabled", true);
    	 }
    }
    
    if(zt=="1"){
   	 document.getElementById('name').setAttribute("readOnly", true);
   	 document.getElementById('surname').setAttribute("readOnly", true);
   	 document.getElementById('birthday').setAttribute("readOnly", true);
   	 document.getElementById('province').setAttribute("disabled", true);
    	 document.getElementById('country').setAttribute("disabled", true);
    	 
   	 document.getElementById('identityNum').setAttribute("readOnly", true);
   	 document.getElementById('city').setAttribute("readOnly", true);
   	 document.getElementById('tel').setAttribute("readOnly", true);
   	 document.getElementById('adress').setAttribute("readOnly", true);
   	if( document.getElementById('button1')!=null){
		document.getElementById('button1').setAttribute("disabled", true); 
	 }
	if( document.getElementById('button2')!=null){
	 document.getElementById('button2').setAttribute("disabled", true);
	 }
	if( document.getElementById('button3')!=null){
	 document.getElementById('button3').setAttribute("disabled", true);
	 }
   }
    
    if(zt=="2"){
   	 document.getElementById('name').setAttribute("readOnly", true);
   	 document.getElementById('surname').setAttribute("readOnly", true);
   	 document.getElementById('birthday').setAttribute("readOnly", true);
   	 document.getElementById('province').setAttribute("disabled", true);
    	document.getElementById('country').setAttribute("disabled", true);
    	 
   	 document.getElementById('identityNum').setAttribute("readOnly", true);
   	 document.getElementById('city').setAttribute("readOnly", true);
   	 document.getElementById('tel').setAttribute("readOnly", true);
   	 document.getElementById('adress').setAttribute("readOnly", true);
   	 if( document.getElementById('button1')!=null){
    		document.getElementById('button1').setAttribute("disabled", true); 
    	 }
    	if( document.getElementById('button2')!=null){
    	 document.getElementById('button2').setAttribute("disabled", true);
   	 }
    	if( document.getElementById('button3')!=null){
    	 document.getElementById('button3').setAttribute("disabled", true);
   	 }
   }
    
    function toUpdate(){
    	
    	 document.getElementById("name").readOnly=false;
       	 document.getElementById("surname").readOnly=false;
       	 document.getElementById("birthday").readOnly=false;
    	 // document.getElementById("country").attr("disabled",false);
    	  //document.getElementById("province").attr("disabled",false);
    	 
    	  $('#country').attr("disabled","");
    	   $('#province').attr("disabled","");
    	    $("#country").removeAttr("disabled");
    	  $("#province").removeAttr("disabled");
    	  	$("#country").css('pointer-events', 'auto');
       	 document.getElementById("identityNum").readOnly=false;
       	 document.getElementById("city").readOnly=false;
       	 document.getElementById("tel").readOnly=false;
       	 document.getElementById("adress").readOnly=false;
       	 
       
       	
       	document.getElementById("xxxxx2").style.display="block";
       	
       	pdgjhjzd();
   	 if(document.getElementById('button1')!=null){
    		document.getElementById('button1').disabled=false;
    	 }
    	if(document.getElementById('button2')!=null){
    	 document.getElementById('button2').disabled=false;
   	 }
    	if(document.getElementById('button3')!=null){
    	 document.getElementById('button3').disabled=false;
   	 }
    	
    	
    	$("#tjbutton").removeClass("disabled");
    	$("#tjbutton").removeClass("btn-secondary");
    	$("#tjbutton").addClass("btn-primary");
    	
    	$("#tjbutton3").removeClass("btn-primary");
    	$("#tjbutton3").addClass("btn-secondary");
    	$("#tjbutton3").addClass("disabled");
    	
    	$("#tjbutton").attr("onclick", "toSave()");
       	
    }
    
    
    
    if(document.getElementById("name").value!=""){
    	 document.getElementById('name').setAttribute("readOnly", true);
       	 document.getElementById('surname').setAttribute("readOnly", true);
       	 document.getElementById('birthday').setAttribute("readOnly", true);
       //	 document.getElementById('province').setAttribute("disabled", true);
       	// document.getElementById('country').setAttribute("disabled", true);
       	$("#country").css('pointer-events', 'none');
    }
    
    </script>
    
   	<script  th:inline="javascript">

function updateaLang(obj){

	    document.getElementById("planguage").value=obj;
		$.ajax({
			//几个参数需要注意一下
			type: "POST",//方法类型
			url: "saveLanguage.do" ,//url
			data: $('#languageForm').serialize(),
			success: function (result) {
				
				location.reload();
			},
			error : function() {
			
			}
		});
	
	
}
</script>	
<script src="https://cdn.customgpt.ai/js/chat.js"  th:inline="javascript" th:if="${FOOTER2_SCRIPT_STATUS} eq '1'"></script>
<script th:if="${FOOTER2_SCRIPT_STATUS} eq '1'" th:inline="javascript" >window.onload = function () { CustomGPT.init({p_id: [[${JS2_ID}]], p_key: [[${JS2_KEY}]], reset_conversation: "1"}); };</script>
 
<!-- Start of seventyinvestech Zendesk Widget script -->
<script id="ze-snippet" th:src="@{'https://static.zdassets.com/ekr/snippet.js?key='+${JS_KEY}}"  th:if="${FOOTER_SCRIPT_STATUS} eq '1'"> </script >
<!-- End of seventyinvestech Zendesk Widget script -->
  </body>
  </body>
</html>