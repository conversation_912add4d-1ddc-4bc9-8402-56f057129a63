package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.TradeAccountService;


public class MyWebsocketClient4FundInfo extends WebSocketClient {
	private TradeAccountService tradeAccountService;
	

	private FundInfoService fundInfoService;
	
	private FundInfo fundInfo;
	
	
	
	
	
	
	public FundInfo getFundInfo() {
		return fundInfo;
	}

	public void setFundInfo(FundInfo fundInfo) {
		this.fundInfo = fundInfo;
	}

	public FundInfoService getFundInfoService() {
		return fundInfoService;
	}

	public void setFundInfoService(FundInfoService fundInfoService) {
		this.fundInfoService = fundInfoService;
	}

	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}

	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}

	public MyWebsocketClient4FundInfo(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			
			JSONObject jsStr = JSONObject.parseObject(arg0);
			
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
			if(jsStr.get("error").toString().equals("")) {
					fundInfo.setOperStatus(1);
					fundInfo.setOrderId(jsStr.get("orderid").toString());
					this.fundInfoService.saveOrUpdate(fundInfo);
					if(jsStr.get("operationtype").toString().equals("1")){//入金
						
					}else if(jsStr.get("operationtype").toString().equals("2")) {//出金
						//扣除冻结的金额
						
						    TradeAccount query=new TradeAccount();
	    					query.setTradeId(fundInfo.getTradeId());
	    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
	    					if(ta_page.getContent().size()>0) {
	    						TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
	    						
	    						if(ta!=null) {
	    							if(ta.getBalance6()!=null) {
	    								if(ta.getBalance6()>=fundInfo.getAmount()) {
	    									ta.setBalance6(ta.getBalance6()-fundInfo.getAmount());
	    								}else {
	    									ta.setBalance6(0D);
	    								}
	    								this.tradeAccountService.saveOrUpdate(ta);
	    							}
	    						}
	    					}
						
					
                           						
					}
					
				
				}else {
					fundInfo.setOperStatus(2);
					this.fundInfoService.saveOrUpdate(fundInfo);
					
						//  close();
					
				}
			
			
		}
		new Thread(){
    		public void run(){
    			try {
    			Thread.sleep(30000L);
    			     close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}
