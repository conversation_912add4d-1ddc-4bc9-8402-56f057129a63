
package com.ews.crm.controller;

import java.io.BufferedReader;
import java.io.FileOutputStream;
import java.io.IOException;
import java.io.InputStreamReader;
import java.io.OutputStreamWriter;
import java.net.HttpURLConnection;
import java.net.URI;
import java.net.URL;
import java.net.URLDecoder;
import java.net.URLEncoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;

import org.apache.commons.codec.digest.DigestUtils;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.HttpClient;
import com.ews.common.MyWebsocketClient4FundInfo;
import com.ews.common.RandomStrUtil;
import com.ews.common.Result;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.entity.DepositBank;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserGroup;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.entity.WithdrawalBank;
import com.ews.crm.service.CompanyInfoService;
import com.ews.crm.service.DepositBankService;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserGroupService;
import com.ews.crm.service.UserInfoService;
import com.ews.crm.service.WithdrawalBankService;
import com.ews.crm.service.WithdrawalSettingService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;
import com.wx.core.util.HttpClientUtil;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.drafts.Draft_6455;



@RestController
@RequestMapping("/admin/fundInfo")
public class FundInfoController {
	@Autowired
	private FundInfoService fundInfoService;
	
	@Autowired
	private WithdrawalBankService withdrawalBankService;
	
	@Autowired
	private DepositBankService depositBankService;
	
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private CompanyInfoService companyInfoService;
	
	@Autowired
	private UserGroupService userGroupService;
	
	

	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;
	
	@Autowired
	private OperLogService operLogService;
	
	@Autowired
	private ConstantConfig constantConfig;
	@Autowired
	private WithdrawalSettingService withdrawalSettingService;


   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	FundInfo query  = new FundInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
               	query.setCrmAccount(request.getParameter("crmAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
               	query.setOrderId(request.getParameter("orderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
               	query.setBankName(request.getParameter("bankName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
               	query.setBankNum(request.getParameter("bankNum").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
               	query.setAccountName(request.getParameter("accountName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
               	query.setMobile(request.getParameter("mobile").trim());
        	}
        
        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
               	query.setBankAddress(request.getParameter("bankAddress").trim());
        	}
        	Page<FundInfo> pages = fundInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<FundInfo> fundInfos = pages.getContent();
        	for(int i=0;i<fundInfos.size();i++) {
        		FundInfo entity  = fundInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
        		
        	}
        	
        	datas.put("items", fundInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}

    
    
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/withdrawList")
    public ResponseData withdrawList(HttpServletRequest request) {
    	try {
        	FundInfo query  = new FundInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
               	query.setCrmAccount(request.getParameter("crmAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
        	}
        	
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
               	query.setOrderId(request.getParameter("orderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
               	query.setBankName(request.getParameter("bankName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
               	query.setBankNum(request.getParameter("bankNum").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
               	query.setAccountName(request.getParameter("accountName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
               	query.setMobile(request.getParameter("mobile").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
               	query.setBankAddress(request.getParameter("bankAddress").trim());
        	}
        	
        	
        	
        	 List userList=new ArrayList();
        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
             if(loginUser!=null) {
             	
            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
            	 
            	 if(uu!=null&&uu.getUserId()!=null) {
            		 
            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
            			
            			 //1.查出自己及名下所有的代理
            			 
            			 User user_query=new User();
            			 user_query.setSortStr(uu.getSortStr());
            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
            			
            			 
            			 //2.遍历代理查出所有的crm用户ID
            			 
            			
            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
            			 
            			 UserInfo ui_self_query=new UserInfo();
            			 ui_self_query.setParentId(uu.getUserId());
            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
            			 for(int m=0;m<ui_self.getContent().size();m++) {
            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
            				 userList.add(ui_1.getId());
            				 
            			 }
            			 if(ui_self.getContent().size()<=0) {
            				 userList.add(999999L);
            			 }
            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
            			 
            			 for(int n=0;n<ul.getContent().size();n++) {
            				 User user_1=ul.getContent().get(n);
            				 UserInfo ui_self_query_2=new UserInfo();
            				 ui_self_query_2.setParentId(user_1.getUserId());
	            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
	            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
	            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
	            				 userList.add(ui_2.getId());
	            			 }
            			 }
            			 query.setUserInfoList(userList);
            		 }
            	 }else {
            		 userList.add(-99999999L);
            		 query.setUserInfoList(userList);
            	 }
             }else {
            	 userList.add(-99999999L);
        		 query.setUserInfoList(userList);
             }
             
             
        	query.setType(2);
        	Page<FundInfo> pages = fundInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<FundInfo> fundInfos = pages.getContent();
        	for(int i=0;i<fundInfos.size();i++) {
        		FundInfo entity  = fundInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
               if(entity.getWithdrawBankId()!=null) {
        			 WithdrawalBank  withdrawalBank =(WithdrawalBank)withdrawalBankService.findById(entity.getWithdrawBankId());
        			 if(withdrawalBank!=null) {
        			 entity.setBackup6(withdrawalBank.getBankName()+" "+withdrawalBank.getBankAccount()+" "+withdrawalBank.getAccountName() );
        			 }
        		 }
        	}
        	
        	datas.put("items", fundInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/depositList")
    public ResponseData depositList(HttpServletRequest request) {
    	try {
        	FundInfo query  = new FundInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
               	query.setCrmAccount(request.getParameter("crmAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
               	query.setOrderId(request.getParameter("orderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
               	query.setBankName(request.getParameter("bankName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
               	query.setBankNum(request.getParameter("bankNum").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
               	query.setAccountName(request.getParameter("accountName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
               	query.setMobile(request.getParameter("mobile").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
               	query.setBankAddress(request.getParameter("bankAddress").trim());
        	}
        	
        	 List userList=new ArrayList();
        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
             if(loginUser!=null) {
             	
            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
            	 
            	 if(uu!=null&&uu.getUserId()!=null) {
            		 
            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
            			
            			 //1.查出自己及名下所有的代理
            			 
            			 User user_query=new User();
            			 user_query.setSortStr(uu.getSortStr());
            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
            			
            			 
            			 //2.遍历代理查出所有的crm用户ID
            			 
            			
            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
            			 
            			 UserInfo ui_self_query=new UserInfo();
            			 ui_self_query.setParentId(uu.getUserId());
            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
            			 for(int m=0;m<ui_self.getContent().size();m++) {
            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
            				 userList.add(ui_1.getId());
            				 
            			 }
            			 if(ui_self.getContent().size()<=0) {
            				 userList.add(999999L);
            			 }
            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
            			 
            			 for(int n=0;n<ul.getContent().size();n++) {
            				 User user_1=ul.getContent().get(n);
            				 UserInfo ui_self_query_2=new UserInfo();
            				 ui_self_query_2.setParentId(user_1.getUserId());
	            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
	            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
	            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
	            				 userList.add(ui_2.getId());
	            			 }
            			 }
            			 query.setUserInfoList(userList);
            		 }
            	 }else {
            		 userList.add(-99999999L);
            		 query.setUserInfoList(userList);
            	 }
             }else {
        		 userList.add(-99999999L);
        		 query.setUserInfoList(userList);
        	 }
             
           // query.setIsAvailable(1);
        	query.setType(1);
        	Page<FundInfo> pages = fundInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<FundInfo> fundInfos = pages.getContent();
        	for(int i=0;i<fundInfos.size();i++) {
        		FundInfo entity  = fundInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
        		 
                 if(entity.getDepositBankId()!=null) {
          			 DepositBank depositBank =(DepositBank)this.depositBankService.findById(entity.getDepositBankId());
          			 if(depositBank!=null) {
          			 entity.setBackup6(depositBank.getBankName()+" "+depositBank.getBankAccount()+" "+depositBank.getAccountName() );
          			 }
          		 }
        	}
        	
        	datas.put("items", fundInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    
    
    
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/withdrawAuditList")
    public ResponseData withdrawAuditList(HttpServletRequest request) {
    	try {
    		
    		System.out.println("b3"+request.getParameter("backup3"));
    		System.out.println("b4"+request.getParameter("backup4"));
    		System.out.println("cc"+request.getParameter("currencyType"));
        	FundInfo query  = new FundInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
               	query.setCrmAccount(request.getParameter("crmAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("backup3"))) {
               	query.setBackup3(new Integer(request.getParameter("backup3")));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("backup4"))) {
               	query.setBackup4(new Integer(request.getParameter("backup4")));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("currencyType"))) {
               	query.setCurrencyType(new Integer(request.getParameter("currencyType")));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
               	query.setOrderId(request.getParameter("orderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
               	query.setBankName(request.getParameter("bankName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
               	query.setBankNum(request.getParameter("bankNum").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
               	query.setAccountName(request.getParameter("accountName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
               	query.setMobile(request.getParameter("mobile").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
               	query.setBankAddress(request.getParameter("bankAddress").trim());
        	}
        	query.setType(2);
        	query.setAuditStatus(0);
        	Page<FundInfo> pages = fundInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<FundInfo> fundInfos = pages.getContent();
        	//查出出金银行
			WithdrawalBank bank_query=new WithdrawalBank();
			bank_query.setIsShow(1);
			Page<WithdrawalBank> bank_page=this.withdrawalBankService.findAll(0,100,"id","asc", bank_query);
			
        	for(int i=0;i<fundInfos.size();i++) {
        		FundInfo entity  = fundInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
               if(entity.getWithdrawBankId()!=null) {
        			 WithdrawalBank  withdrawalBank =(WithdrawalBank)withdrawalBankService.findById(entity.getWithdrawBankId());
        			 if(withdrawalBank!=null) {
        			 entity.setBackup6(withdrawalBank.getBankName()+" "+withdrawalBank.getBankAccount()+" "+withdrawalBank.getAccountName() );
        			 }
        		 }
               
               //显示余额
               TradeAccount query_ta=new TradeAccount();
               query_ta.setTradeId(entity.getTradeId());
				Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query_ta);
				if(ta_page.getContent().size()>0) {
					TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
					entity.setAccountAmount(ta.getBalance1());
					
					
					
				}
			
				entity.setBankList(bank_page.getContent());
        	}
        	
        	datas.put("items", fundInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    
    /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/depositAuditList")
    public ResponseData depositAuditList(HttpServletRequest request) {
    	try {
        	FundInfo query  = new FundInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
               	query.setUserName(request.getParameter("userName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
               	query.setCrmAccount(request.getParameter("crmAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
               	query.setOrderId(request.getParameter("orderId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
               	query.setBankName(request.getParameter("bankName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
               	query.setBankNum(request.getParameter("bankNum").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
               	query.setAccountName(request.getParameter("accountName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
               	query.setMobile(request.getParameter("mobile").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
               	query.setBankAddress(request.getParameter("bankAddress").trim());
        	}
        	query.setIsAvailable(1);
        	query.setType(1);
        	query.setAuditStatus(0);
        	Page<FundInfo> pages = fundInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<FundInfo> fundInfos = pages.getContent();
        	for(int i=0;i<fundInfos.size();i++) {
        		FundInfo entity  = fundInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
        		 
                 if(entity.getDepositBankId()!=null) {
          			 DepositBank depositBank =(DepositBank)this.depositBankService.findById(entity.getDepositBankId());
          			 if(depositBank!=null) {
          			 entity.setBackup6(depositBank.getBankName()+" "+depositBank.getBankAccount()+" "+depositBank.getAccountName() );
          			 }
          		 }
                 
                 TradeAccount query_ta=new TradeAccount();
                 query_ta.setTradeId(entity.getTradeId());
  				Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query_ta);
  				if(ta_page.getContent().size()>0) {
  					TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
  					entity.setAccountAmount(ta.getBalance1());
  					
  				  UserGroup ug_query=new UserGroup();
				  ug_query.setGroupName(ta.getGroupName());
				  Page<UserGroup> page_group=this.userGroupService.findAll(0,10,"id","asc", ug_query);
				  if(page_group.getContent().size()>0) {
						UserGroup ug_t=(UserGroup)page_group.getContent().get(0);
						entity.setGroupInfo(ug_t.getGroupName()+"  "+ug_t.getBackup1());
				  }
  					
  				}
                 
               
        	}
        	
        	datas.put("items", fundInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    

	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			FundInfo fundInfo = new FundInfo();
        	fundInfo.setUserId(data.getLong("userId"));
        	fundInfo.setUserName(data.getString("userName"));
        	fundInfo.setCrmAccount(data.getString("crmAccount"));
        	fundInfo.setTradeId(data.getString("tradeId"));
        	fundInfo.setAmount(data.getDouble("amount"));
        	fundInfo.setDepositBankId(data.getLong("depositBankId"));
        	fundInfo.setWithdrawBankId(data.getLong("withdrawBankId"));
        	fundInfo.setAllocationAmount(data.getDouble("allocationAmount"));
        	fundInfo.setAuditStatus(data.getInteger("auditStatus"));
        	fundInfo.setOperStatus(data.getInteger("operStatus"));
        	fundInfo.setAuditId(data.getLong("auditId"));
        	fundInfo.setOrderId(data.getString("orderId"));
        	fundInfo.setBankName(data.getString("bankName"));
        	fundInfo.setBankNum(data.getString("bankNum"));
        	fundInfo.setAccountName(data.getString("accountName"));
        	fundInfo.setMobile(data.getString("mobile"));
        	fundInfo.setBankAddress(data.getString("bankAddress"));
        	fundInfo.setRemark(data.getString("remark"));
        	fundInfo.setAnnex(data.getString("annex"));
        	fundInfo.setFee(data.getDouble("fee"));
        	fundInfo.setActualAmount(data.getDouble("actualAmount"));
        	
			Result re = fundInfoService.saveOrUpdate(fundInfo);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(fundInfo);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			FundInfo fundInfo = this.fundInfoService.findById(id);
				if (fundInfo != null) {
					fundInfo.setUserId(data.getLong("userId"));
					fundInfo.setUserName(data.getString("userName"));
					fundInfo.setCrmAccount(data.getString("crmAccount"));
					fundInfo.setTradeId(data.getString("tradeId"));
					fundInfo.setAmount(data.getDouble("amount"));
					fundInfo.setDepositBankId(data.getLong("depositBankId"));
					fundInfo.setWithdrawBankId(data.getLong("withdrawBankId"));
					fundInfo.setAllocationAmount(data.getDouble("allocationAmount"));
					fundInfo.setAuditStatus(data.getInteger("auditStatus"));
					fundInfo.setOperStatus(data.getInteger("operStatus"));
					fundInfo.setAuditId(data.getLong("auditId"));
					fundInfo.setOrderId(data.getString("orderId"));
					fundInfo.setBankName(data.getString("bankName"));
					fundInfo.setBankNum(data.getString("bankNum"));
					fundInfo.setAccountName(data.getString("accountName"));
					fundInfo.setMobile(data.getString("mobile"));
					fundInfo.setBankAddress(data.getString("bankAddress"));
					fundInfo.setRemark(data.getString("remark"));
					fundInfo.setAnnex(data.getString("annex"));
					fundInfo.setFee(data.getDouble("fee"));
					fundInfo.setActualAmount(data.getDouble("actualAmount"));
					Result re = fundInfoService.saveOrUpdate(fundInfo);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = fundInfoService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = fundInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	/**
	 * 入金审核--通过
	 * @param request
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/auditDepositFundInfo")
	public ResponseData auditDepositFundInfo(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			    Long id = data.getLong("id");
	 			    FundInfo fundInfo = this.fundInfoService.findById(id);
	 			   fundInfo.setBackup5(data.getString("backup5"));
	 			  if(fundInfo!=null&&fundInfo.getAuditStatus().intValue()!=1) {  
	 			    fundInfo.setAuditStatus(1);
	 			    this.fundInfoService.saveOrUpdate(fundInfo);
	 			    
	 			   try {
	 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
	 				    MyWebsocketClient4FundInfo client= new MyWebsocketClient4FundInfo(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
	 					client.setTradeAccountService(this.tradeAccountService);
	 					client.setFundInfoService(this.fundInfoService);
	 					client.setFundInfo(fundInfo);
	 					client.connect();
	 					int mm=0;
	 					Thread.sleep(300);
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(100);
							System.out.println("deposit   "+new Date()+" "+client.getReadyState());
							mm=mm+1;
							if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								client.reconnect();
							mm=0;
							}
						}
	 					    HashMap map=new HashMap();
	 					    map.put("reqtype", "deposituserinfo");
	 						map.put("reqid", String.valueOf(new Date().getTime()));
	 						map.put("login", new Integer(fundInfo.getTradeId()));
	 						
	 						map.put("operationtype", 1);  //1 表示入金，2表示出金 ,3 增加信用，4 减少信用
	 						map.put("amount", fundInfo.getAmount());
	 						//map.put("endtime", 1867656947L);
	 						map.put("comment", data.getString("backup5"));
	 						
	 					     JSONObject jsonObj=new JSONObject(map);
	 					     client.send(jsonObj.toString());
	 					     
	 					     if(fundInfo.getAllocationAmount().doubleValue()>0) {
	 					    	Thread.sleep(300);
	 					    	     mm=0;
									while (!client.getReadyState().equals(READYSTATE.OPEN)) {
										Thread.sleep(100);
										System.out.println("completRegister   "+new Date()+" "+client.getReadyState());
										mm=mm+1;
										if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
											client.reconnect();
										mm=0;
										}
									}
	 					    
	 					     HashMap map2=new HashMap();
	 					     map2.put("reqtype", "deposituserinfo");
	 					     map2.put("reqid", String.valueOf(new Date().getTime()));
	 					     map2.put("login", new Integer(fundInfo.getTradeId()));
	 						
	 					     map2.put("operationtype", 3);  //1 表示入金，2表示出金 ,3 增加信用，4 减少信用
	 					     map2.put("amount", fundInfo.getAllocationAmount());
	 					     map2.put("endtime", 1867656947L);
	 					     map2.put("comment", "Credit In");
	 						
	 					    JSONObject jsonObj2=new JSONObject(map2);
	 					    client.send(jsonObj2.toString());
	 					     }
	 					    
	 					}catch(Exception e) {
	 						
	 					}
	 			   
	 				//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(3);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
				
					return ResponseDataUtil.buildSuccess();
	 			  }else {
	 				 return ResponseDataUtil.buildError("操作错误");
	 			  }
	 		}else{
				return ResponseDataUtil.buildError("缺少必要参数 id");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
	/**
	 * 入金审核-驳回
	 * @param request
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/auditDepositFundInfo2")
	public ResponseData auditDepositFundInfo2(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			    Long id = data.getLong("id");
	 			    FundInfo fundInfo = this.fundInfoService.findById(id);
	 			   fundInfo.setBackup5(data.getString("backup5"));
	 			  if(fundInfo!=null&&fundInfo.getAuditStatus().intValue()!=2) {  
	 			    fundInfo.setAuditStatus(2);
	 			    
	 			    this.fundInfoService.saveOrUpdate(fundInfo);
	 			    
	 			    
	 			   TradeAccount query=new TradeAccount();
					query.setTradeId(fundInfo.getTradeId());
					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
					if(ta_page.getContent().size()>0) {
						TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
						
						if(ta!=null) {
							if(ta.getBalance6()!=null) {
								if(ta.getBalance6()>=fundInfo.getAmount()) {
									ta.setBalance6(ta.getBalance6()-fundInfo.getAmount());
								}else {
									ta.setBalance6(0D);
								}
								this.tradeAccountService.saveOrUpdate(ta);
							}
						}
					}
					
	 			    
	 			    //操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(3);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					return ResponseDataUtil.buildSuccess();
	 			  }else {
	 				 return ResponseDataUtil.buildError("oper error");
	 			  }
	 		}else{
				return ResponseDataUtil.buildError("param id error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
	/**
	 * 出金审核  --通过
	 * @param request
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/auditWithdrawFundInfo")
	public ResponseData auditWithdrawFundInfo(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			    FundInfo fundInfo = this.fundInfoService.findById(id);
	 			   fundInfo.setBackup5(data.getString("backup5"));
	 			    
	 			    if(fundInfo!=null&&fundInfo.getAuditStatus().intValue()!=1) {
	 			   fundInfo.setAuditStatus(1);
	 			    this.fundInfoService.saveOrUpdate(fundInfo);
	 			    
	 			   try {
	 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
	 				    MyWebsocketClient4FundInfo client= new MyWebsocketClient4FundInfo(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
	 					client.setTradeAccountService(this.tradeAccountService);
	 					client.setFundInfoService(this.fundInfoService);
	 					client.setFundInfo(fundInfo);
	 					client.connect();
	 					Thread.sleep(300);
	 					int mm=0;
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(100);
							System.out.println("withdraw   "+new Date()+" "+client.getReadyState());
							mm=mm+1;
							if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								client.reconnect();
							mm=0;
							}
						}
	 					    HashMap map=new HashMap();
	 					    map.put("reqtype", "deposituserinfo");
	 						map.put("reqid", String.valueOf(new Date().getTime()));
	 						map.put("login", new Integer(fundInfo.getTradeId()));
	 						
	 						map.put("operationtype", 2);  //1 表示入金，2表示出金 ,3 增加信用，4 减少信用
	 						map.put("amount", fundInfo.getAmount());
	 						//map.put("endtime", 1867656947L);
	 						map.put("comment", data.getString("backup5"));
	 						
	 					     JSONObject jsonObj=new JSONObject(map);
	 					     client.send(jsonObj.toString());
	 					    
	 					}catch(Exception e) {
	 						
	 					}
	 			    //操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(4);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
				
					return ResponseDataUtil.buildSuccess();
	 		}else {
	 			return ResponseDataUtil.buildError("操作错误");
	 		}
	 		}else{
				return ResponseDataUtil.buildError("缺少必要参数 id");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
	


	/**
	 * 出金审核  --驳回
	 * @param request
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/auditWithdrawFundInfo2")
	public ResponseData auditWithdrawFundInfo2(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			    FundInfo fundInfo = this.fundInfoService.findById(id);
	 			   fundInfo.setBackup5(data.getString("backup5"));
	 			    
	 			    if(fundInfo!=null&&fundInfo.getAuditStatus().intValue()!=2) {
	 			    
	 			
	 			    
	 			    
	 			    if(fundInfo.getOperStatus().intValue()!=1) {// 待出金&& 出金失败
	 			    	  TradeAccount ta_query=new TradeAccount();
	 		 			   ta_query.setTradeId(fundInfo.getTradeId());
	 		 			   Page<TradeAccount> ta_page=this.tradeAccountService.findAll(0,1,"id","asc", ta_query);
	 		 			   if(ta_page.getContent().size()>0) {
	 		 				  TradeAccount ta=(TradeAccount)ta_page.getContent().get(0);
	 		 				  if(ta.getBalance6()!=null) {
	 		 					  ta.setBalance6(ta.getBalance6()-fundInfo.getAmount());
	 		 				  }
	 		 				  
	 		 				  if(fundInfo.getAuditStatus().intValue()!=2) {
	 		 					fundInfo.setAuditStatus(2);
	 		 				    this.fundInfoService.saveOrUpdate(fundInfo);
	 		 				  this.tradeAccountService.saveOrUpdate(ta);
	 		 				  }
	 		 			   }
	 			    }else {
	 			    	
	 			    	if(fundInfo.getAuditStatus().intValue()!=1) {
	 			    		ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
	 			    		
	 			    		
	 			    		 HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
	 	 			      	boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
	 	 			        
	 	 			      	
	 	 			   	if(blll) {
	 			 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(fundInfo.getTradeId()),"2",String.valueOf(fundInfo.getAmount()),"Refusal of deposit");
	 			 			  if(json.getString("retcode").equals("0 Done"))  {
	 			 				 fundInfo.setOperStatus(3);
				            	    fundInfo.setAuditStatus(2);
			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
	 		 				 	  TradeAccount query=new TradeAccount();
	 		    					query.setTradeId(fundInfo.getTradeId());
	 		    					Page<TradeAccount>  ta_page22=this.tradeAccountService.findAll(0,1,"id","asc", query);
	 		    					if(ta_page22.getContent().size()>0) {
	 		    						TradeAccount tasss=(TradeAccount)ta_page22.getContent().get(0);
	 		    						
	 		    						if(tasss!=null) {
	 		    							if(tasss.getBalance6()!=null) {
	 		    								if(tasss.getBalance6()>=fundInfo.getAmount()) {
	 		    									tasss.setBalance6(tasss.getBalance6()-fundInfo.getAmount());
	 		    								}else {
	 		    									tasss.setBalance6(0D);
	 		    								}
	 		    								this.tradeAccountService.saveOrUpdate(tasss);
	 		    							}
	 		    						}
	 		    						
	 		    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
	 		    						 if(json_ta.getString("retcode").equals("0 Done"))  {
	 		    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
	 	    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
	 	    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
	 	    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
	 	    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
	 	    			                	 this.tradeAccountService.saveOrUpdate(tasss);
	 		    							 
	 		    						 }
	 		    						 
	 		    					}
	 		    						 
	 			 			  }else {
	 			 				 fundInfo.setOperStatus(4);
				            	    fundInfo.setAuditStatus(2);
			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
	 			 			  }
	 	 			   	}else {
	 	 			   	 fundInfo.setOperStatus(4);
		            	    fundInfo.setAuditStatus(2);
	 				 		this.fundInfoService.saveOrUpdate(fundInfo);
	 	 			   	}
	 	 			   	
	 			    	}
	 			    	 
	 			    }
	 			    
	 			    //操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(4);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					return ResponseDataUtil.buildSuccess();
	 		}else {
	 			return ResponseDataUtil.buildError("oper error");
	 		}
	 		}else{
				return ResponseDataUtil.buildError("param id error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
	
	    @PostMapping("/exportDepositeExcel")
	    public ResponseData exportDepositeExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = {  "CRM用户姓名", "CRM账号","交易账号","入金金额","配资情况","审核状态","处理状态","订单号","支付渠道","支付账户","姓名","手机号","入金账户","申请时间"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*256);
	        sheet.setColumnWidth(3, (short)15*256);
	        sheet.setColumnWidth(4, (short)15*356);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setColumnWidth(7, (short)15*256);
	        sheet.setColumnWidth(8, (short)15*256);
	        sheet.setColumnWidth(9, (short)15*256);
	        sheet.setColumnWidth(10, (short)15*256);
	        sheet.setColumnWidth(11, (short)15*256);
	        sheet.setColumnWidth(12, (short)15*256);
	        sheet.setColumnWidth(13, (short)15*356);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
			    FundInfo query  = new FundInfo();
	        
		    	String sortBy = "desc";
		    	String sort = "id";
		    	if (request.getParameter("sort")!= null) {
		    		sort = request.getParameter("sort").trim();
		    		if (sort.startsWith("+")) {
		    			sortBy = "asc";
		    		}
		  			sort = sort.replace("+", "").replace("-", "");
		    	}
	        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
	               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
	               	query.setUserName(request.getParameter("userName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
	               	query.setCrmAccount(request.getParameter("crmAccount").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
	               	query.setTradeId(request.getParameter("tradeId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
	               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
	               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
	               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
	               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
	               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
	               	query.setOrderId(request.getParameter("orderId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
	               	query.setBankName(request.getParameter("bankName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
	               	query.setBankNum(request.getParameter("bankNum").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
	               	query.setAccountName(request.getParameter("accountName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
	               	query.setMobile(request.getParameter("mobile").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
	               	query.setBankAddress(request.getParameter("bankAddress").trim());
	        	}
	        	
	        	
	        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			
	            			 //1.查出自己及名下所有的代理
	            			 
	            			 User user_query=new User();
	            			 user_query.setSortStr(uu.getSortStr());
	            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	            			
	            			 
	            			 //2.遍历代理查出所有的crm用户ID
	            			 
	            			 List userList=new ArrayList();
	            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
	            			 
	            			 UserInfo ui_self_query=new UserInfo();
	            			 ui_self_query.setParentId(uu.getUserId());
	            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
	            			 for(int m=0;m<ui_self.getContent().size();m++) {
	            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
	            				 userList.add(ui_1.getId());
	            				 
	            			 }
	            			 if(ui_self.getContent().size()<=0) {
	            				 userList.add(999999L);
	            			 }
	            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
	            			 
	            			 for(int n=0;n<ul.getContent().size();n++) {
	            				 User user_1=ul.getContent().get(n);
	            				 UserInfo ui_self_query_2=new UserInfo();
	            				 ui_self_query_2.setParentId(user_1.getUserId());
		            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
		            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
		            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
		            				 userList.add(ui_2.getId());
		            			 }
	            			 }
	            			 query.setUserInfoList(userList);
	            		 }
	            	 }
	             }
	        	query.setType(1);
	        	Page<FundInfo> pages = fundInfoService.findAll(0, 100000, sort, sortBy, query);
	        	List<FundInfo> fundInfos = pages.getContent();
	        	for(int i=0;i<fundInfos.size();i++) {
	        		FundInfo entity  = fundInfos.get(i);
	                 if(entity.getDepositBankId()!=null) {
	          			 DepositBank depositBank =(DepositBank)this.depositBankService.findById(entity.getDepositBankId());
	          			 if(depositBank!=null) {
	          			 entity.setBackup6(depositBank.getBankName()+" "+depositBank.getBankAccount()+" "+depositBank.getAccountName() );
	          			 }
	          		 }
	        	}
			 for(int k=0;k<fundInfos.size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   FundInfo entity  = fundInfos.get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(entity.getUserName()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(entity.getCrmAccount()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(entity.getTradeId()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(entity.getAmount().toString()));
	               row.createCell(4).setCellValue(new HSSFRichTextString(entity.getAllocationAmount().toString()));
	               
	               if(entity.getAuditStatus().intValue()==0) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("待审核")); 
	               }else  if(entity.getAuditStatus().intValue()==1) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("审核通过"));
	               }else  if(entity.getAuditStatus().intValue()==2) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("驳回"));
	               }
	               
	               if(entity.getOperStatus().intValue()==0) {
	            	   row.createCell(6).setCellValue(new HSSFRichTextString("待处理")); 
	               }else  if(entity.getOperStatus().intValue()==1) {
	            	   row.createCell(6).setCellValue(new HSSFRichTextString("处理成功"));
	               }else  if(entity.getOperStatus().intValue()==2) {
	            	   row.createCell(6).setCellValue(new HSSFRichTextString("处理失败"));
	               }
	               row.createCell(7).setCellValue(new HSSFRichTextString(entity.getOrderId()));
	               row.createCell(8).setCellValue(new HSSFRichTextString(entity.getBankName()));
	               row.createCell(9).setCellValue(new HSSFRichTextString(entity.getBankNum()));
	               row.createCell(10).setCellValue(new HSSFRichTextString(entity.getAccountName()));
	               row.createCell(11).setCellValue(new HSSFRichTextString(entity.getMobile()));
	               row.createCell(12).setCellValue(new HSSFRichTextString(entity.getBackup6()));
	               row.createCell(13).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
	               
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}
	
	    @PostMapping("/exportWithdrawExcel")
	    public ResponseData exportWithdrawExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = {  "CRM用户姓名", "CRM账号","交易账号","出金金额","审核状态","处理状态","订单号","收款渠道","收款账户","姓名","手机号","付款账户","申请时间"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*256);
	        sheet.setColumnWidth(3, (short)15*256);
	        sheet.setColumnWidth(4, (short)15*356);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setColumnWidth(7, (short)15*256);
	        sheet.setColumnWidth(8, (short)15*256);
	        sheet.setColumnWidth(9, (short)15*256);
	        sheet.setColumnWidth(10, (short)15*256);
	        sheet.setColumnWidth(11, (short)15*256);
	        sheet.setColumnWidth(12, (short)15*356);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
				FundInfo query  = new FundInfo();
		    	String sortBy = "desc";
		    	String sort = "id";
		    	if (request.getParameter("sort")!= null) {
		    		sort = request.getParameter("sort").trim();
		    		if (sort.startsWith("+")) {
		    			sortBy = "asc";
		    		}
		  			sort = sort.replace("+", "").replace("-", "");
		    	}
	        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
	               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("userName"))) {
	               	query.setUserName(request.getParameter("userName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("crmAccount"))) {
	               	query.setCrmAccount(request.getParameter("crmAccount").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
	               	query.setTradeId(request.getParameter("tradeId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("depositBankId"))) {
	               	query.setDepositBankId(Long.parseLong(request.getParameter("depositBankId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("withdrawBankId"))) {
	               	query.setWithdrawBankId(Long.parseLong(request.getParameter("withdrawBankId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("auditStatus"))) {
	               	query.setAuditStatus(Integer.parseInt(request.getParameter("auditStatus").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("operStatus"))) {
	               	query.setOperStatus(Integer.parseInt(request.getParameter("operStatus").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("auditId"))) {
	               	query.setAuditId(Long.parseLong(request.getParameter("auditId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("orderId"))) {
	               	query.setOrderId(request.getParameter("orderId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankName"))) {
	               	query.setBankName(request.getParameter("bankName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankNum"))) {
	               	query.setBankNum(request.getParameter("bankNum").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("accountName"))) {
	               	query.setAccountName(request.getParameter("accountName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("mobile"))) {
	               	query.setMobile(request.getParameter("mobile").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("bankAddress"))) {
	               	query.setBankAddress(request.getParameter("bankAddress").trim());
	        	}
	        	
	        	
	        	
	        	
	        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			
	            			 //1.查出自己及名下所有的代理
	            			 
	            			 User user_query=new User();
	            			 user_query.setSortStr(uu.getSortStr());
	            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	            			
	            			 
	            			 //2.遍历代理查出所有的crm用户ID
	            			 
	            			 List userList=new ArrayList();
	            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
	            			 
	            			 UserInfo ui_self_query=new UserInfo();
	            			 ui_self_query.setParentId(uu.getUserId());
	            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
	            			 for(int m=0;m<ui_self.getContent().size();m++) {
	            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
	            				 userList.add(ui_1.getId());
	            				 
	            			 }
	            			 if(ui_self.getContent().size()<=0) {
	            				 userList.add(999999L);
	            			 }
	            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
	            			 
	            			 for(int n=0;n<ul.getContent().size();n++) {
	            				 User user_1=ul.getContent().get(n);
	            				 UserInfo ui_self_query_2=new UserInfo();
	            				 ui_self_query_2.setParentId(user_1.getUserId());
		            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
		            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
		            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
		            				 userList.add(ui_2.getId());
		            			 }
	            			 }
	            			 query.setUserInfoList(userList);
	            		 }
	            	 }
	             }
	             
	             
	        	query.setType(2);
	        	Page<FundInfo> pages = fundInfoService.findAll(0, 100000, sort, sortBy, query);
	        	List<FundInfo> fundInfos = pages.getContent();
	        	for(int i=0;i<fundInfos.size();i++) {
	        		FundInfo entity  = fundInfos.get(i);
	               if(entity.getWithdrawBankId()!=null) {
	        			 WithdrawalBank  withdrawalBank =(WithdrawalBank)withdrawalBankService.findById(entity.getWithdrawBankId());
	        			 if(withdrawalBank!=null) {
	        			 entity.setBackup6(withdrawalBank.getBankName()+" "+withdrawalBank.getBankAccount()+" "+withdrawalBank.getAccountName() );
	        			 }
	        		 }
	        	}
			 for(int k=0;k<fundInfos.size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   FundInfo entity  = fundInfos.get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(entity.getUserName()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(entity.getCrmAccount()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(entity.getTradeId()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(entity.getAmount().toString()));
	               
	               if(entity.getAuditStatus().intValue()==0) {
	            	   row.createCell(4).setCellValue(new HSSFRichTextString("待审核")); 
	               }else  if(entity.getAuditStatus().intValue()==1) {
	            	   row.createCell(4).setCellValue(new HSSFRichTextString("审核通过"));
	               }else  if(entity.getAuditStatus().intValue()==2) {
	            	   row.createCell(4).setCellValue(new HSSFRichTextString("驳回"));
	               }
	               
	               if(entity.getOperStatus().intValue()==0) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("待处理")); 
	               }else  if(entity.getOperStatus().intValue()==1) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("处理成功"));
	               }else  if(entity.getOperStatus().intValue()==2) {
	            	   row.createCell(5).setCellValue(new HSSFRichTextString("处理失败"));
	               }
	               row.createCell(6).setCellValue(new HSSFRichTextString(entity.getOrderId()));
	               row.createCell(7).setCellValue(new HSSFRichTextString(entity.getBankName()));
	               row.createCell(8).setCellValue(new HSSFRichTextString(entity.getBankNum()));
	               row.createCell(9).setCellValue(new HSSFRichTextString(entity.getAccountName()));
	               row.createCell(10).setCellValue(new HSSFRichTextString(entity.getMobile()));
	               row.createCell(11).setCellValue(new HSSFRichTextString(entity.getBackup6()));
	               row.createCell(12).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
	               
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}
	    
	    
	    @GetMapping("/qxzxs")
		public ResponseData qxzxs(@Valid Long id, HttpServletRequest request) {
			if (id != null) { 
				try {
					FundInfo fundInfo=this.fundInfoService.findById(id);
					fundInfo.setOperStatus(1);
					//fundInfo.setAuditStatus(1);
					Result	result=this.fundInfoService.saveOrUpdate(fundInfo);
					if(result.getCode().equals(Result.CODESUCCESS)){
						return ResponseDataUtil.buildSuccess(); 
					}else{
						return ResponseDataUtil.buildError("cancel fail"); 
					}
				}catch(Exception e){
					e.printStackTrace();
					return ResponseDataUtil.buildError(e.getMessage());
	 			}
			} else {
				return ResponseDataUtil.buildError("param error");		}
		}
	    
	    
	    @GetMapping("/cxzxs")
	 		public ResponseData cxzxs(@Valid Long id, HttpServletRequest request) {
	 			if (id != null) { 
	 				try {
	 					
	 					
	 					
	 					FundInfo fundInfo=this.fundInfoService.findById(id);
	 					
	 					
	 					
	 					if(fundInfo.getType().intValue()==1) {//入金
	 						
	 						 try {
	 		 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
	 		 				   HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
	 		 			      	boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
	 		 			        
	 		 			      	
	 		 			   	if(blll) {
	 				 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(fundInfo.getTradeId()),"2",String.valueOf(fundInfo.getAmount()),fundInfo.getRemark());
	 				 			  if(json.getString("retcode").equals("0 Done"))  {
	 				 				 fundInfo.setOperStatus(1);
	 			 			         //   fundInfo.setOrderId(json.getString("data"));
	 			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
	 			 				 		
	 			 				 	  TradeAccount query=new TradeAccount();
	 			    					query.setTradeId(fundInfo.getTradeId());
	 			    					Page<TradeAccount>  ta_page22=this.tradeAccountService.findAll(0,1,"id","asc", query);
	 			    					if(ta_page22.getContent().size()>0) {
	 			    						TradeAccount tasss=(TradeAccount)ta_page22.getContent().get(0);
	 			    						
	 			    						if(tasss!=null) {
	 			    							if(tasss.getBalance6()!=null) {
	 			    								if(tasss.getBalance6()>=fundInfo.getAmount()) {
	 			    									tasss.setBalance6(tasss.getBalance6()-fundInfo.getAmount());
	 			    								}else {
	 			    									tasss.setBalance6(0D);
	 			    								}
	 			    								this.tradeAccountService.saveOrUpdate(tasss);
	 			    							}
	 			    						}
	 			    						
	 			    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
	 			    						 if(json_ta.getString("retcode").equals("0 Done"))  {
	 			    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
	 		    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
	 		    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
	 		    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
	 		    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
	 		    			                	 this.tradeAccountService.saveOrUpdate(tasss);
	 			    							 
	 			    						 }
	 			    						 
	 			    					}
	 			    						 
	 				 			  }else {
	 				 				   fundInfo.setOperStatus(2);
	 			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
	 				 			  }
	 		 			   	}else {
	 		 			   	    fundInfo.setOperStatus(2);
	 					 		this.fundInfoService.saveOrUpdate(fundInfo);
	 		 			   	}
	 		 					}catch(Exception e) {
	 		 						
	 		 					}
	 						
	 					}else {//出金
	 						
	 						
	 						 try {
		 		 				    ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
		 		 				   HttpClient httpClient = new HttpClient(ss.getApiAddress()); 
		 		 			      	boolean blll=httpClient.sendAuth(ss.getBackup1(), ss.getBackup2(), "484", "api");
		 		 			        
		 		 			      	
		 		 			   	if(blll) {
		 				 			   org.json.JSONObject json=httpClient.balanceCharge(String.valueOf(fundInfo.getTradeId()),"2",String.valueOf(fundInfo.getAmount()*-1),fundInfo.getRemark());
		 				 			  if(json.getString("retcode").equals("0 Done"))  {
		 				 				 fundInfo.setOperStatus(1);
		 			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
		 			 				 		
		 			 				 	  TradeAccount query=new TradeAccount();
		 			    					query.setTradeId(fundInfo.getTradeId());
		 			    					Page<TradeAccount>  ta_page22=this.tradeAccountService.findAll(0,1,"id","asc", query);
		 			    					if(ta_page22.getContent().size()>0) {
		 			    						TradeAccount tasss=(TradeAccount)ta_page22.getContent().get(0);
		 			    						
		 			    						if(tasss!=null) {
		 			    							if(tasss.getBalance6()!=null) {
		 			    								if(tasss.getBalance6()>=fundInfo.getAmount()) {
		 			    									tasss.setBalance6(tasss.getBalance6()-fundInfo.getAmount());
		 			    								}else {
		 			    									tasss.setBalance6(0D);
		 			    								}
		 			    								this.tradeAccountService.saveOrUpdate(tasss);
		 			    							}
		 			    						}
		 			    						
		 			    						 org.json.JSONObject json_ta=httpClient.getTradeInfoByLoginid(tasss.getTradeId());
		 			    						 if(json_ta.getString("retcode").equals("0 Done"))  {
		 			    							org.json.JSONObject ja55=(org.json.JSONObject)json_ta.get("answer");
		 		    			                	tasss.setBalance1(new Double(ja55.getString("Balance")));
		 		    			                	tasss.setBalance2(new Double(ja55.getString("Credit")));
		 		    			                	tasss.setBalance3(new Double(ja55.getString("Equity")));
		 		    			                	tasss.setBalance4(new Double(ja55.getString("MarginFree")));
		 		    			                	 this.tradeAccountService.saveOrUpdate(tasss);
		 			    							 
		 			    						 }
		 			    						 
		 			    					}
		 			    						 
		 				 			  }else {
		 				 				   fundInfo.setOperStatus(2);
		 			 				 		this.fundInfoService.saveOrUpdate(fundInfo);
		 				 			  }
		 		 			   	}else {
		 		 			   	    fundInfo.setOperStatus(2);
		 					 		this.fundInfoService.saveOrUpdate(fundInfo);
		 		 			   	}
		 		 					}catch(Exception e) {
		 		 						
		 		 					}
 						
	 						
	 						
	 						
	 					}
	 					
	 					
	 					Result	result=this.fundInfoService.saveOrUpdate(fundInfo);
	 					if(result.getCode().equals(Result.CODESUCCESS)){
	 						return ResponseDataUtil.buildSuccess(); 
	 					}else{
	 						return ResponseDataUtil.buildError("cancel fail"); 
	 					}
	 				}catch(Exception e){
	 					e.printStackTrace();
	 					return ResponseDataUtil.buildError(e.getMessage());
	 	 			}
	 			} else {
	 				return ResponseDataUtil.buildError("param error");		}
	 		}
	


}

