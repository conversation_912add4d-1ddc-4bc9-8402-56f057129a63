<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USK外汇CRM - 个人信息</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(26, 31, 46, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.2);
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            overflow: hidden;
        }

        .nav-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .nav-title {
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-left: 50px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #007AFF;
            background: rgba(0, 122, 255, 0.1);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 主要内容区域 */
        .main-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
        }

        /* 内容网格布局 */
        .content-grid {
            display: grid;
            grid-template-columns: 300px 1fr;
            gap: 30px;
        }

        /* 左侧边栏 */
        .sidebar {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        .profile-card {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 25px;
            text-align: center;
            position: relative;
            overflow: hidden;
        }

        .profile-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #00FF88);
        }

        .profile-avatar {
            width: 80px;
            height: 80px;
            margin: 0 auto 15px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 24px;
            font-weight: bold;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
            position: relative;
            cursor: pointer;
        }

        .avatar-upload {
            position: absolute;
            bottom: 0;
            right: 0;
            width: 24px;
            height: 24px;
            background: #007AFF;
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            color: white;
            cursor: pointer;
            border: 2px solid rgba(26, 31, 46, 1);
        }

        .profile-name {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 5px;
        }

        .profile-email {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            margin-bottom: 15px;
        }

        .verification-status {
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
            margin-bottom: 15px;
        }

        .status-badge {
            padding: 4px 12px;
            border-radius: 12px;
            font-size: 12px;
            font-weight: 500;
        }

        .status-verified {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
        }

        .status-pending {
            background: rgba(255, 149, 0, 0.2);
            color: #FF9500;
        }

        .profile-stats {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
            margin-top: 20px;
        }

        .stat-item {
            text-align: center;
            padding: 12px;
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
        }

        .stat-value {
            font-size: 18px;
            font-weight: 600;
            color: #00FF88;
            font-family: 'Courier New', monospace;
        }

        .stat-label {
            font-size: 11px;
            color: rgba(255, 255, 255, 0.6);
            margin-top: 4px;
        }

        /* 导航菜单 */
        .profile-nav {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 20px;
        }

      

        .profile-nav-item {
            display: flex;
            align-items: center;
            gap: 12px;
            padding: 12px 15px;
            border-radius: 8px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            margin-bottom: 5px;
            text-decoration: none;
        }

        .profile-nav-item:hover, .profile-nav-item.active {
            background: rgba(0, 122, 255, 0.1);
            color: #007AFF;
        }

        .nav-icon {
            font-size: 16px;
            width: 20px;
            text-align: center;
        }

        /* 主要内容区域 */
        .main-content {
            display: flex;
            flex-direction: column;
            gap: 25px;
        }

        .content-section {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            overflow: hidden;
        }

        .section-header {
            padding: 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .edit-btn {
            padding: 8px 16px;
            background: transparent;
            border: 1px solid rgba(0, 122, 255, 0.3);
            border-radius: 6px;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .edit-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        .section-content {
            padding: 25px;
        }

        /* 表单样式 */
        .form-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .form-group {
            display: flex;
            flex-direction: column;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            padding: 14px 16px;
            background: rgba(45, 58, 79, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 10px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #007AFF;
            background: rgba(45, 58, 79, 0.8);
            box-shadow: 0 0 15px rgba(0, 122, 255, 0.2);
        }

        .form-input:disabled {
            background: rgba(45, 58, 79, 0.3);
            color: rgba(255, 255, 255, 0.5);
            cursor: not-allowed;
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* 信息展示样式 */
        .info-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
        }

        .info-item {
            display: flex;
            flex-direction: column;
            gap: 6px;
        }

        .info-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 13px;
            font-weight: 500;
        }

        .info-value {
            color: #ffffff;
            font-size: 15px;
            font-weight: 500;
        }

        .info-value.empty {
            color: rgba(255, 255, 255, 0.4);
            font-style: italic;
        }

        /* 银行卡片 */
        .bank-cards {
            display: grid;
            gap: 15px;
        }

        .bank-card {
            background: rgba(45, 58, 79, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            transition: all 0.3s ease;
        }

        .bank-card:hover {
            border-color: rgba(0, 122, 255, 0.4);
            background: rgba(45, 58, 79, 0.6);
        }

        .bank-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .bank-name {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .bank-actions {
            display: flex;
            gap: 10px;
        }

        .bank-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .bank-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        .bank-details {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 15px;
        }

        /* 文件上传区域 */
        .upload-section {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
        }

        .upload-card {
            background: rgba(45, 58, 79, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            padding: 20px;
            text-align: center;
            transition: all 0.3s ease;
        }

        .upload-card:hover {
            border-color: rgba(0, 122, 255, 0.4);
        }

        .upload-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            justify-content: center;
            gap: 8px;
        }

        .upload-area {
            border: 2px dashed rgba(0, 122, 255, 0.3);
            border-radius: 8px;
            padding: 30px 20px;
            background: rgba(0, 122, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
            position: relative;
        }

        .upload-area:hover {
            border-color: rgba(0, 122, 255, 0.6);
            background: rgba(0, 122, 255, 0.1);
        }

        .upload-area.has-file {
            border-color: #00FF88;
            background: rgba(0, 255, 136, 0.1);
        }

        .upload-icon {
            font-size: 32px;
            margin-bottom: 10px;
            color: #007AFF;
        }

        .upload-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 5px;
        }

        .upload-hint {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        .file-preview {
            display: none;
            align-items: center;
            gap: 10px;
            margin-top: 10px;
        }

        .file-preview.show {
            display: flex;
        }

        .file-info {
            flex: 1;
            text-align: left;
        }

        .file-name {
            color: #ffffff;
            font-size: 14px;
            font-weight: 500;
        }

        .file-size {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        .file-actions {
            display: flex;
            gap: 8px;
        }

        .file-btn {
            padding: 4px 8px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 4px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-size: 11px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .file-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        /* 保存按钮 */
        .save-section {
            display: flex;
            justify-content: flex-end;
            gap: 15px;
            padding: 20px 25px;
            background: rgba(0, 0, 0, 0.1);
            border-top: 1px solid rgba(255, 255, 255, 0.1);
        }

        .save-btn {
            padding: 12px 24px;
            border: none;
            border-radius: 8px;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .save-btn.cancel {
            background: transparent;
            border: 1px solid rgba(255, 255, 255, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }

        .save-btn.cancel:hover {
            background: rgba(255, 255, 255, 0.1);
        }

        .save-btn.primary {
            background: linear-gradient(135deg, #007AFF, #00FF88);
            color: #ffffff;
        }

        .save-btn.primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(0, 122, 255, 0.4);
        }

        /* 内容显示/隐藏 */
        .content-section {
            display: none;
        }

        .content-section.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .sidebar {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .profile-card, .profile-nav {
                min-width: 250px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
            }
            
            .form-grid, .info-grid {
                grid-template-columns: 1fr;
            }
            
            .upload-section {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
            
            .sidebar {
                flex-direction: column;
            }
            
            .profile-card, .profile-nav {
                min-width: auto;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-left">
            <div class="nav-logo"><img src="logo.png" alt="USK"></div>
            <div class="nav-title">USK FOREX CRM</div>
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-item">账户总览</a>
                <a href="fund-management.html" class="nav-item">资金管理</a>
                <a href="trading-records.html" class="nav-item">交易记录</a>
                <a href="profile.html" class="nav-item active">个人信息</a>
            </div>
        </div>
        <div class="nav-right">
            <div class="nav-user">
                <div class="user-avatar">张</div>
                <span>张先生</span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">个人信息</h1>
            <p class="page-subtitle">管理您的个人资料和账户设置</p>
        </div>

        <div class="content-grid">
            <!-- 左侧边栏 -->
            <div class="sidebar">
                <!-- 用户信息卡片 -->
                <div class="profile-card">
                    <div class="profile-avatar" onclick="uploadAvatar()">
                        张
                        <div class="avatar-upload">📷</div>
                    </div>
                    <div class="profile-name">张先生</div>
                    <div class="profile-email"><EMAIL></div>
                    
                    <div class="verification-status">
                        <span class="status-badge status-verified">✅ 已认证</span>
                    </div>
                    
                    <div class="profile-stats">
                        <div class="stat-item">
                            <div class="stat-value">4</div>
                            <div class="stat-label">交易账户</div>
                        </div>
                        <div class="stat-item">
                            <div class="stat-value">98%</div>
                            <div class="stat-label">资料完整度</div>
                        </div>
                    </div>
                </div>

                <!-- 导航菜单 -->
                <div class="profile-nav">
                    <div class="nav-title">
                        ⚙️ 设置菜单
                    </div>
                    <a href="#" class="profile-nav-item active" data-section="basic-info">
                        <span class="nav-icon">👤</span>
                        <span>基础信息</span>
                    </a>
                    <a href="#" class="profile-nav-item" data-section="bank-info">
                        <span class="nav-icon">🏦</span>
                        <span>银行信息</span>
                    </a>
                    <a href="#" class="profile-nav-item" data-section="documents">
                        <span class="nav-icon">📄</span>
                        <span>身份文件</span>
                    </a>
                    <a href="#" class="profile-nav-item" data-section="security">
                        <span class="nav-icon">🔒</span>
                        <span>安全设置</span>
                    </a>
                  
                </div>
            </div>

            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 基础信息 -->
                <div class="content-section active" id="basic-info">
                    <div class="section-header">
                        <h3 class="section-title">
                            👤 基础信息
                        </h3>
                        <button class="edit-btn" onclick="toggleEdit('basic-info')">
                            ✏️ 编辑信息
                        </button>
                    </div>
                    <div class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">姓名</label>
                                <input type="text" class="form-input" value="张先生" disabled id="name-input">
                            </div>
                            <div class="form-group">
                                <label class="form-label">性别</label>
                                <select class="form-select" disabled id="gender-select">
                                    <option value="male" selected>男</option>
                                    <option value="female">女</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">出生日期</label>
                                <input type="date" class="form-input" value="1985-06-15" disabled id="birth-input">
                            </div>
                            <div class="form-group">
                                <label class="form-label">国籍</label>
                                <select class="form-select" disabled id="nationality-select">
                                    <option value="CN" selected>中国</option>
                                    <option value="HK">香港</option>
                                    <option value="TW">台湾</option>
                                    <option value="US">美国</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">居住国家</label>
                                <select class="form-select" disabled id="residence-select">
                                    <option value="CN" selected>中国</option>
                                    <option value="HK">香港</option>
                                    <option value="SG">新加坡</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">身份证号码</label>
                                <input type="text" class="form-input" value="320123198506150012" disabled id="id-input">
                            </div>
                            <div class="form-group">
                                <label class="form-label">手机号码</label>
                                <input type="tel" class="form-input" value="+86 138 0000 0000" disabled id="phone-input">
                            </div>
                            <div class="form-group">
                                <label class="form-label">邮箱地址</label>
                                <input type="email" class="form-input" value="<EMAIL>" disabled id="email-input">
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">居住地址</label>
                                <input type="text" class="form-input" value="江苏省南京市鼓楼区..." disabled id="address-input">
                            </div>
                        </div>
                    </div>
                    <div class="save-section" id="basic-info-actions" style="display: none;">
                        <button class="save-btn cancel" onclick="cancelEdit('basic-info')">
                            ❌ 取消
                        </button>
                        <button class="save-btn primary" onclick="saveBasicInfo()">
                            💾 保存更改
                        </button>
                    </div>
                </div>

                <!-- 银行信息 -->
                <div class="content-section" id="bank-info">
                    <div class="section-header">
                        <h3 class="section-title">
                            🏦 银行信息
                        </h3>
                        <button class="edit-btn" onclick="addBankCard()">
                            ➕ 添加银行卡
                        </button>
                    </div>
                    <div class="section-content">
                        <div class="bank-cards">
                            <div class="bank-card">
                                <div class="bank-header">
                                    <div class="bank-name">
                                        🏛️ 中国工商银行
                                    </div>
                                    <div class="bank-actions">
                                        <button class="bank-btn" onclick="editBank(1)">编辑</button>
                                        <button class="bank-btn" onclick="deleteBank(1)">删除</button>
                                    </div>
                                </div>
                                <div class="bank-details">
                                    <div class="info-item">
                                        <div class="info-label">开户行</div>
                                        <div class="info-value">工商银行南京分行</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">银行卡号</div>
                                        <div class="info-value">**** **** **** 1234</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">户名</div>
                                        <div class="info-value">张先生</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">卡片类型</div>
                                        <div class="info-value">储蓄卡</div>
                                    </div>
                                </div>
                            </div>

                            <div class="bank-card">
                                <div class="bank-header">
                                    <div class="bank-name">
                                        🏛️ 中国农业银行
                                    </div>
                                    <div class="bank-actions">
                                        <button class="bank-btn" onclick="editBank(2)">编辑</button>
                                        <button class="bank-btn" onclick="deleteBank(2)">删除</button>
                                    </div>
                                </div>
                                <div class="bank-details">
                                    <div class="info-item">
                                        <div class="info-label">开户行</div>
                                        <div class="info-value">农业银行江苏分行</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">银行卡号</div>
                                        <div class="info-value">**** **** **** 5678</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">户名</div>
                                        <div class="info-value">张先生</div>
                                    </div>
                                    <div class="info-item">
                                        <div class="info-label">卡片类型</div>
                                        <div class="info-value">储蓄卡</div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 身份文件 -->
                <div class="content-section" id="documents">
                    <div class="section-header">
                        <h3 class="section-title">
                            📄 身份文件
                        </h3>
                        <button class="edit-btn" onclick="refreshDocuments()">
                            🔄 刷新状态
                        </button>
                    </div>
                    <div class="section-content">
                        <div class="upload-section">
                            <div class="upload-card">
                                <div class="upload-title">
                                    🆔 身份证正面
                                </div>
                                <div class="upload-area has-file" onclick="uploadFile('id-front')">
                                    <div class="upload-icon">✅</div>
                                    <div class="upload-text">身份证正面已上传</div>
                                    <div class="upload-hint">ID_Front_20250130.jpg</div>
                                    <div class="file-preview show">
                                        <div class="file-info">
                                            <div class="file-name">ID_Front_20250130.jpg</div>
                                            <div class="file-size">2.3 MB • 上传于 2025-01-30</div>
                                        </div>
                                        <div class="file-actions">
                                            <button class="file-btn" onclick="previewFile('id-front')">预览</button>
                                            <button class="file-btn" onclick="deleteFile('id-front')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-card">
                                <div class="upload-title">
                                    🆔 身份证反面
                                </div>
                                <div class="upload-area has-file" onclick="uploadFile('id-back')">
                                    <div class="upload-icon">✅</div>
                                    <div class="upload-text">身份证反面已上传</div>
                                    <div class="upload-hint">ID_Back_20250130.jpg</div>
                                    <div class="file-preview show">
                                        <div class="file-info">
                                            <div class="file-name">ID_Back_20250130.jpg</div>
                                            <div class="file-size">2.1 MB • 上传于 2025-01-30</div>
                                        </div>
                                        <div class="file-actions">
                                            <button class="file-btn" onclick="previewFile('id-back')">预览</button>
                                            <button class="file-btn" onclick="deleteFile('id-back')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-card">
                                <div class="upload-title">
                                    💳 银行卡照片
                                </div>
                                <div class="upload-area" onclick="uploadFile('bank-card')">
                                    <div class="upload-icon">📤</div>
                                    <div class="upload-text">点击上传银行卡照片</div>
                                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                                    <div class="file-preview">
                                        <div class="file-info">
                                            <div class="file-name"></div>
                                            <div class="file-size"></div>
                                        </div>
                                        <div class="file-actions">
                                            <button class="file-btn" onclick="previewFile('bank-card')">预览</button>
                                            <button class="file-btn" onclick="deleteFile('bank-card')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>

                            <div class="upload-card">
                                <div class="upload-title">
                                    📱 支付宝收款码
                                </div>
                                <div class="upload-area" onclick="uploadFile('alipay')">
                                    <div class="upload-icon">📤</div>
                                    <div class="upload-text">点击上传支付宝收款码</div>
                                    <div class="upload-hint">支持 JPG、PNG 格式</div>
                                    <div class="file-preview">
                                        <div class="file-info">
                                            <div class="file-name"></div>
                                            <div class="file-size"></div>
                                        </div>
                                        <div class="file-actions">
                                            <button class="file-btn" onclick="previewFile('alipay')">预览</button>
                                            <button class="file-btn" onclick="deleteFile('alipay')">删除</button>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- 安全设置 -->
                <div class="content-section" id="security">
                    <div class="section-header">
                        <h3 class="section-title">
                            🔒 安全设置
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="info-grid">
                            <div class="info-item">
                                <div class="info-label">登录密码</div>
                                <div class="info-value">••••••••</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">上次修改</div>
                                <div class="info-value">2025-01-15</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">双因子认证</div>
                                <div class="info-value">已启用</div>
                            </div>
                            <div class="info-item">
                                <div class="info-label">登录设备</div>
                                <div class="info-value">3台设备</div>
                            </div>
                        </div>
                        <div style="margin-top: 20px;">
                            <button class="save-btn cancel" onclick="changePassword()">
                                🔑 修改密码
                            </button>
                        </div>
                    </div>
                </div>

                <!-- 通知设置 -->
                <div class="content-section" id="notifications">
                    <div class="section-header">
                        <h3 class="section-title">
                            🔔 通知设置
                        </h3>
                    </div>
                    <div class="section-content">
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">邮件通知</label>
                                <select class="form-select">
                                    <option value="all">接收所有通知</option>
                                    <option value="important" selected>仅重要通知</option>
                                    <option value="none">关闭通知</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">短信通知</label>
                                <select class="form-select">
                                    <option value="all">接收所有通知</option>
                                    <option value="important" selected>仅重要通知</option>
                                    <option value="none">关闭通知</option>
                                </select>
                            </div>
                        </div>
                    </div>
                    <div class="save-section">
                        <button class="save-btn primary" onclick="saveNotifications()">
                            💾 保存设置
                        </button>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 导航菜单切换
        document.querySelectorAll('.profile-nav-item').forEach(item => {
            item.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 移除所有活动状态
                document.querySelectorAll('.profile-nav-item').forEach(nav => nav.classList.remove('active'));
                document.querySelectorAll('.content-section').forEach(section => section.classList.remove('active'));
                
                // 添加活动状态
                this.classList.add('active');
                const sectionId = this.getAttribute('data-section');
                document.getElementById(sectionId).classList.add('active');
            });
        });

        // 编辑信息切换
        function toggleEdit(sectionId) {
            const inputs = document.querySelectorAll(`#${sectionId} .form-input, #${sectionId} .form-select`);
            const actions = document.getElementById(`${sectionId}-actions`);
            const editBtn = document.querySelector(`#${sectionId} .edit-btn`);
            
            inputs.forEach(input => {
                input.disabled = !input.disabled;
            });
            
            if (actions) {
                actions.style.display = actions.style.display === 'none' ? 'flex' : 'none';
            }
            
            editBtn.innerHTML = editBtn.innerHTML.includes('编辑') ? '❌ 取消编辑' : '✏️ 编辑信息';
        }

        // 取消编辑
        function cancelEdit(sectionId) {
            toggleEdit(sectionId);
            // 重置表单值到原始状态
            document.getElementById('name-input').value = '张先生';
            document.getElementById('phone-input').value = '+86 138 0000 0000';
            // ... 其他字段重置
        }

        // 保存基础信息
        function saveBasicInfo() {
            const formData = {
                name: document.getElementById('name-input').value,
                gender: document.getElementById('gender-select').value,
                birth: document.getElementById('birth-input').value,
                nationality: document.getElementById('nationality-select').value,
                residence: document.getElementById('residence-select').value,
                idNumber: document.getElementById('id-input').value,
                phone: document.getElementById('phone-input').value,
                email: document.getElementById('email-input').value,
                address: document.getElementById('address-input').value
            };
            
            console.log('保存基础信息:', formData);
            
            // 模拟保存过程
            const saveBtn = document.querySelector('#basic-info .save-btn.primary');
            saveBtn.innerHTML = '💾 保存中...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.innerHTML = '✅ 保存成功';
                setTimeout(() => {
                    toggleEdit('basic-info');
                    saveBtn.innerHTML = '💾 保存更改';
                    saveBtn.disabled = false;
                }, 1000);
            }, 2000);
        }

        // 头像上传
        function uploadAvatar() {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    console.log('上传头像:', file.name);
                    // 模拟上传成功
                    alert('头像上传成功！');
                }
            };
            input.click();
        }

        // 文件上传
        function uploadFile(type) {
            const input = document.createElement('input');
            input.type = 'file';
            input.accept = 'image/*,.pdf';
            input.onchange = function(e) {
                const file = e.target.files[0];
                if (file) {
                    console.log(`上传${type}文件:`, file.name);
                    
                    // 更新UI显示已上传状态
                    const uploadArea = event.currentTarget;
                    const card = uploadArea.closest('.upload-card');
                    const preview = card.querySelector('.file-preview');
                    const fileName = card.querySelector('.file-name');
                    const fileSize = card.querySelector('.file-size');
                    
                    uploadArea.classList.add('has-file');
                    uploadArea.querySelector('.upload-icon').textContent = '✅';
                    uploadArea.querySelector('.upload-text').textContent = '文件上传成功';
                    uploadArea.querySelector('.upload-hint').textContent = file.name;
                    
                    fileName.textContent = file.name;
                    fileSize.textContent = `${(file.size / 1024 / 1024).toFixed(1)} MB • 上传于 ${new Date().toLocaleDateString()}`;
                    preview.classList.add('show');
                }
            };
            input.click();
        }

        // 预览文件
        function previewFile(type) {
            console.log('预览文件:', type);
            alert('文件预览功能开发中...');
        }

        // 删除文件
        function deleteFile(type) {
            if (confirm('确定要删除这个文件吗？')) {
                console.log('删除文件:', type);
                
                // 重置UI状态
                const card = event.target.closest('.upload-card');
                const uploadArea = card.querySelector('.upload-area');
                const preview = card.querySelector('.file-preview');
                
                uploadArea.classList.remove('has-file');
                uploadArea.querySelector('.upload-icon').textContent = '📤';
                uploadArea.querySelector('.upload-text').textContent = '点击上传文件';
                uploadArea.querySelector('.upload-hint').textContent = '支持 JPG、PNG 格式';
                preview.classList.remove('show');
            }
        }

        // 添加银行卡
        function addBankCard() {
            console.log('添加银行卡');
            alert('添加银行卡功能开发中...');
        }

        // 编辑银行信息
        function editBank(id) {
            console.log('编辑银行信息:', id);
            alert('编辑银行信息功能开发中...');
        }

        // 删除银行信息
        function deleteBank(id) {
            if (confirm('确定要删除这张银行卡吗？')) {
                console.log('删除银行信息:', id);
                alert('银行卡删除成功！');
            }
        }

        // 修改密码
        function changePassword() {
            console.log('修改密码');
            alert('修改密码功能开发中...');
        }

        // 保存通知设置
        function saveNotifications() {
            console.log('保存通知设置');
            
            const saveBtn = event.target;
            saveBtn.innerHTML = '💾 保存中...';
            saveBtn.disabled = true;
            
            setTimeout(() => {
                saveBtn.innerHTML = '✅ 保存成功';
                setTimeout(() => {
                    saveBtn.innerHTML = '💾 保存设置';
                    saveBtn.disabled = false;
                }, 1000);
            }, 1500);
        }

        // 刷新文档状态
        function refreshDocuments() {
            console.log('刷新文档状态');
            
            const refreshBtn = event.target;
            refreshBtn.innerHTML = '🔄 刷新中...';
            refreshBtn.disabled = true;
            
            setTimeout(() => {
                refreshBtn.innerHTML = '✅ 已更新';
                setTimeout(() => {
                    refreshBtn.innerHTML = '🔄 刷新状态';
                    refreshBtn.disabled = false;
                }, 1000);
            }, 2000);
        }

        // 阻止上传区域默认行为
        document.querySelectorAll('.upload-area').forEach(area => {
            ['dragenter', 'dragover', 'dragleave', 'drop'].forEach(eventName => {
                area.addEventListener(eventName, function(e) {
                    e.preventDefault();
                    e.stopPropagation();
                });
            });

            ['dragenter', 'dragover'].forEach(eventName => {
                area.addEventListener(eventName, function() {
                    this.style.backgroundColor = 'rgba(0, 122, 255, 0.15)';
                });
            });

            ['dragleave', 'drop'].forEach(eventName => {
                area.addEventListener(eventName, function() {
                    this.style.backgroundColor = '';
                });
            });

            area.addEventListener('drop', function(e) {
                const files = e.dataTransfer.files;
                if (files.length > 0) {
                    console.log('拖拽上传文件:', files[0].name);
                    // 处理拖拽上传的文件
                }
            });
        });
    </script>
</body>
</html> 