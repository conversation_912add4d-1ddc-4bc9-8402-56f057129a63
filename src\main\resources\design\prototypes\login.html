<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USK外汇CRM - 登录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0f1419 0%, #1a2332 25%, #2d3a4f 50%, #1a2332 75%, #0f1419 100%);
            min-height: 100vh;
            position: relative;
            overflow: hidden;
        }

        /* 动态背景粒子效果 */
        .particles {
            position: absolute;
            width: 100%;
            height: 100%;
            overflow: hidden;
            z-index: 1;
        }

        .particle {
            position: absolute;
            width: 2px;
            height: 2px;
            background: rgba(0, 122, 255, 0.6);
            border-radius: 50%;
            animation: float 6s ease-in-out infinite;
        }

        @keyframes float {
            0%, 100% { transform: translateY(0px) rotate(0deg); opacity: 0.3; }
            50% { transform: translateY(-20px) rotate(180deg); opacity: 1; }
        }

        /* 金融市场动态线条 */
        .market-lines {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            z-index: 2;
            pointer-events: none;
        }

        .line {
            position: absolute;
            height: 1px;
            background: linear-gradient(90deg, transparent, rgba(0, 255, 136, 0.4), transparent);
            animation: pulse 3s ease-in-out infinite;
        }

        @keyframes pulse {
            0%, 100% { opacity: 0.3; transform: scaleX(1); }
            50% { opacity: 1; transform: scaleX(1.2); }
        }

        /* 主容器 */
        .login-container {
            position: relative;
            z-index: 10;
            display: flex;
            align-items: center;
            justify-content: center;
            min-height: 100vh;
            padding: 20px;
        }

        .login-card {
            background: rgba(26, 35, 50, 0.95);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.2);
            border-radius: 16px;
            box-shadow: 
                0 25px 50px rgba(0, 0, 0, 0.3),
                0 0 80px rgba(0, 122, 255, 0.1),
                inset 0 1px 0 rgba(255, 255, 255, 0.1);
            padding: 60px 50px;
            width: 480px;
            position: relative;
            overflow: hidden;
        }

        .login-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: -100%;
            width: 100%;
            height: 100%;
            background: linear-gradient(90deg, transparent, rgba(0, 122, 255, 0.1), transparent);
            animation: shimmer 3s ease-in-out infinite;
        }

        @keyframes shimmer {
            0% { left: -100%; }
            100% { left: 100%; }
        }

        /* Logo区域 */
        .logo-section {
            text-align: center;
            margin-bottom: 40px;
            position: relative;
        }

        .logo {
            width: 80px;
            height: 80px;
            margin: 0 auto 20px;
            border-radius: 20px;
            display: flex;
            align-items: center;
            justify-content: center;
            position: relative;
            box-shadow: 0 10px 30px rgba(0, 122, 255, 0.3);
        }

        .logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
            border-radius: 20px;
        }

        .company-name {
            color: #ffffff;
            font-size: 28px;
            font-weight: 300;
            letter-spacing: 2px;
            margin-bottom: 8px;
        }

        .tagline {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
            letter-spacing: 1px;
        }

        /* 表单样式 */
        .form-group {
            margin-bottom: 25px;
            position: relative;
        }

        .form-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            margin-bottom: 8px;
            display: block;
            font-weight: 500;
        }

        .form-input {
            width: 100%;
            padding: 16px 20px;
            background: rgba(45, 58, 79, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus {
            outline: none;
            border-color: #007AFF;
            background: rgba(45, 58, 79, 0.8);
            box-shadow: 0 0 20px rgba(0, 122, 255, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* 验证码区域 */
        .captcha-group {
            display: flex;
            gap: 15px;
            align-items: flex-end;
        }

        .captcha-input {
            flex: 1;
        }

        .captcha-image {
            width: 120px;
            height: 54px;
            background: linear-gradient(45deg, #2d3a4f, #1a2332);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            color: #00FF88;
            font-family: 'Courier New', monospace;
            font-size: 20px;
            font-weight: bold;
            letter-spacing: 3px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .captcha-image:hover {
            border-color: #007AFF;
            background: linear-gradient(45deg, #3a4766, #2d3a4f);
        }

        /* 登录按钮 */
        .login-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
            position: relative;
            overflow: hidden;
        }

        .login-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 122, 255, 0.4);
        }

        .login-button:active {
            transform: translateY(0);
        }

        /* 底部链接 */
        .form-footer {
            text-align: center;
            margin-top: 30px;
        }

        .forgot-password {
            color: rgba(255, 255, 255, 0.6);
            text-decoration: none;
            font-size: 14px;
            transition: color 0.3s ease;
        }

        .forgot-password:hover {
            color: #007AFF;
        }

        /* 市场数据显示 */
        .market-ticker {
            position: absolute;
            bottom: 30px;
            left: 50px;
            right: 50px;
            height: 40px;
            background: rgba(0, 0, 0, 0.3);
            border-radius: 20px;
            display: flex;
            align-items: center;
            padding: 0 20px;
            backdrop-filter: blur(10px);
            border: 1px solid rgba(255, 255, 255, 0.1);
        }

        .ticker-item {
            display: flex;
            align-items: center;
            margin-right: 30px;
            font-size: 12px;
        }

        .ticker-symbol {
            color: rgba(255, 255, 255, 0.8);
            margin-right: 8px;
            font-weight: 500;
        }

        .ticker-price {
            color: #00FF88;
            font-family: 'Courier New', monospace;
        }

        .ticker-change {
            margin-left: 5px;
            font-size: 10px;
        }

        .ticker-up { color: #00FF88; }
        .ticker-down { color: #FF3B30; }

        /* 响应式设计 */
        @media (max-width: 768px) {
            .login-card {
                margin: 20px;
                padding: 40px 30px;
            }
            
            .market-ticker {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 动态背景粒子 -->
    <div class="particles" id="particles"></div>
    
    <!-- 金融市场线条 -->
    <div class="market-lines">
        <div class="line" style="top: 20%; width: 300px; left: 10%; animation-delay: 0s;"></div>
        <div class="line" style="top: 40%; width: 200px; right: 15%; animation-delay: 1s;"></div>
        <div class="line" style="top: 60%; width: 250px; left: 20%; animation-delay: 2s;"></div>
        <div class="line" style="top: 80%; width: 180px; right: 25%; animation-delay: 1.5s;"></div>
    </div>

    <div class="login-container">
        <div class="login-card">
            <!-- Logo区域 -->
            <div class="logo-section">
                <div class="logo"><img src="logo.png" alt="USK FOREX CRM"></div>
                <h1 class="company-name">USK FOREX CRM</h1>
                <p class="tagline">专业外汇资金管理平台</p>
            </div>

            <!-- 登录表单 -->
            <form class="login-form">
                <div class="form-group">
                    <label class="form-label">邮箱 / 用户名</label>
                    <input type="text" class="form-input" placeholder="请输入您的邮箱或用户名" >
                </div>

                <div class="form-group">
                    <label class="form-label">密码</label>
                    <input type="password" class="form-input" placeholder="请输入您的密码" >
                </div>

                <div class="form-group">
                    <label class="form-label">验证码</label>
                    <div class="captcha-group">
                        <div class="captcha-input">
                            <input type="text" class="form-input" placeholder="请输入验证码" >
                        </div>
                        <div class="captcha-image" onclick="refreshCaptcha()">
                            <span id="captcha-text">X9K2</span>
                        </div>
                    </div>
                </div>

                <button type="button" onclick="window.location.href='dashboard.html'" class="login-button">
                    立即登录
                </button>
            </form>

            <div class="form-footer">
                <a href="#" class="forgot-password">忘记密码？</a>
            </div>
        </div>
    </div>

    <!-- 实时市场数据 -->
    <div class="market-ticker">
        <div class="ticker-item">
            <span class="ticker-symbol">EUR/USD</span>
            <span class="ticker-price">1.0856</span>
            <span class="ticker-change ticker-up">+0.0023</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-symbol">GBP/USD</span>
            <span class="ticker-price">1.2734</span>
            <span class="ticker-change ticker-down">-0.0015</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-symbol">USD/JPY</span>
            <span class="ticker-price">149.82</span>
            <span class="ticker-change ticker-up">+0.45</span>
        </div>
        <div class="ticker-item">
            <span class="ticker-symbol">AUD/USD</span>
            <span class="ticker-price">0.6623</span>
            <span class="ticker-change ticker-up">+0.0008</span>
        </div>
    </div>

    <script>
        // 生成动态粒子
        function createParticles() {
            const particlesContainer = document.getElementById('particles');
            const particleCount = 50;

            for (let i = 0; i < particleCount; i++) {
                const particle = document.createElement('div');
                particle.className = 'particle';
                particle.style.left = Math.random() * 100 + '%';
                particle.style.top = Math.random() * 100 + '%';
                particle.style.animationDelay = Math.random() * 6 + 's';
                particle.style.animationDuration = (Math.random() * 3 + 4) + 's';
                particlesContainer.appendChild(particle);
            }
        }

        // 刷新验证码
        function refreshCaptcha() {
            const captchaText = document.getElementById('captcha-text');
            const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
            let result = '';
            for (let i = 0; i < 4; i++) {
                result += chars.charAt(Math.floor(Math.random() * chars.length));
            }
            captchaText.textContent = result;
        }

        // 模拟实时市场数据更新
        function updateMarketData() {
            const tickerPrices = document.querySelectorAll('.ticker-price');
            const tickerChanges = document.querySelectorAll('.ticker-change');
            
            tickerPrices.forEach((price, index) => {
                const currentPrice = parseFloat(price.textContent);
                const change = (Math.random() - 0.5) * 0.01;
                const newPrice = currentPrice + change;
                
                price.textContent = newPrice.toFixed(4);
                
                const changeElement = tickerChanges[index];
                if (change > 0) {
                    changeElement.className = 'ticker-change ticker-up';
                    changeElement.textContent = '+' + Math.abs(change).toFixed(4);
                } else {
                    changeElement.className = 'ticker-change ticker-down';
                    changeElement.textContent = '-' + Math.abs(change).toFixed(4);
                }
            });
        }

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            createParticles();
            
            // 每10秒更新一次市场数据
            setInterval(updateMarketData, 10000);
        });
    </script>
</body>
</html> 