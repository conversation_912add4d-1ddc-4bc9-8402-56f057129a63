# CRM系统底部标准规范

## 概述

本文档定义了CRM系统中所有页面底部（页脚）的统一标准规范，确保整个系统的视觉一致性和用户体验的统一性。

## 标准HTML结构

### 基础HTML结构
```html
<!-- 页脚 -->
<footer class="footer footer-transparent d-print-none">
  <div style="max-width: 1200px; margin: 0 auto; padding: 20px; text-align: center;">
    <div style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
      ©Copyright 2025 All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
    </div>
  </div>
</footer>
```

### 关键元素说明

1. **外层容器**
   - 使用 `<footer>` 语义化标签
   - 必须包含类名：`footer footer-transparent d-print-none`

2. **内层容器**
   - 最大宽度：1200px
   - 居中对齐：`margin: 0 auto`
   - 内边距：20px
   - 文本居中：`text-align: center`

3. **版权信息**
   - 文字颜色：`rgba(255, 255, 255, 0.8)`
   - 字体大小：14px
   - 必须包含动态变量：`CRM_VERSION` 和 `CRM_LICENSED`

## 标准CSS样式

### 核心样式规范
```css
/* 页脚样式 - 按照main.html标准 */
.footer {
    background: rgba(26, 31, 46, 0.8);
    backdrop-filter: blur(20px);
    border-top: 1px solid rgba(0, 122, 255, 0.15);
    color: rgba(255, 255, 255, 0.8);
}

.footer a {
    color: rgba(255, 255, 255, 0.9);
    text-decoration: none;
}

.footer a:hover {
    color: #007AFF;
}
```

### 样式说明

1. **背景效果**
   - 半透明背景：`rgba(26, 31, 46, 0.8)`
   - 毛玻璃效果：`backdrop-filter: blur(20px)`
   - 顶部边框：`1px solid rgba(0, 122, 255, 0.15)`

2. **文字样式**
   - 默认文字颜色：`rgba(255, 255, 255, 0.8)`
   - 链接颜色：`rgba(255, 255, 255, 0.9)`
   - 链接悬停：`#007AFF`

3. **其他属性**
   - 移除默认下划线：`text-decoration: none`
   - 响应式支持：自动适配不同屏幕尺寸

## 动态内容规范

### 必须包含的动态变量

1. **版本信息** - `${CRM_VERSION}`
   - 显示当前系统版本号
   - 格式：v + 版本号（如：v1.0.0）

2. **许可信息** - `${CRM_LICENSED}`
   - 显示系统许可对象
   - 格式：Licensed to + 公司名称

### 版权信息格式
```
©Copyright 2025 All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
```

## 响应式设计规范

### 桌面端（>768px）
- 使用标准布局
- 居中显示
- 固定内边距：20px

### 移动端（≤768px）
- 保持相同的视觉效果
- 自动调整容器宽度
- 文字大小保持14px不变

## 主题变量规范

### 颜色主题
- **主背景色**：`rgba(26, 31, 46, 0.8)`
- **主要文字色**：`rgba(255, 255, 255, 0.8)`
- **链接文字色**：`rgba(255, 255, 255, 0.9)`
- **强调色**：`#007AFF`
- **边框色**：`rgba(0, 122, 255, 0.15)`

### 尺寸变量
- **最大宽度**：1200px
- **内边距**：20px
- **字体大小**：14px
- **边框粗细**：1px

## 兼容性要求

### 浏览器支持
- Chrome 60+
- Firefox 55+
- Safari 12+
- Edge 79+

### 特殊效果支持
- `backdrop-filter` 毛玻璃效果
- CSS Variables 支持
- Flexbox 布局支持

## 实施检查清单

### HTML结构检查
- [ ] 使用了正确的 `<footer>` 标签
- [ ] 包含了必需的CSS类名
- [ ] 容器结构完整
- [ ] 动态变量正确引用

### CSS样式检查
- [ ] 页脚背景效果正确
- [ ] 文字颜色符合规范
- [ ] 链接样式完整
- [ ] 响应式适配正常

### 内容检查
- [ ] 版权信息格式正确
- [ ] 动态变量显示正常
- [ ] 文字居中对齐
- [ ] 字体大小符合标准

### 功能检查
- [ ] 打印时正确隐藏（d-print-none）
- [ ] 链接悬停效果正常
- [ ] 在不同设备上显示一致

## 常见问题与解决方案

### 问题1：页脚显示在内容上方
**解决方案**：确保页脚在页面结构的最底部，不要放在其他容器内部。

### 问题2：毛玻璃效果不显示
**解决方案**：检查浏览器是否支持 `backdrop-filter` 属性，对于不支持的浏览器可以提供降级方案。

### 问题3：动态变量不显示
**解决方案**：确保后端正确传递了 `CRM_VERSION` 和 `CRM_LICENSED` 变量。

### 问题4：移动端显示异常
**解决方案**：检查响应式CSS是否正确加载，确保viewport meta标签设置正确。

## 版本历史

### v1.0.0 (2025-01-27)
- 创建初始规范
- 定义基础HTML结构
- 确立CSS样式标准
- 规范动态内容格式

## 维护说明

1. 本规范基于 main.html 的标准底部样式制定
2. 所有新页面必须按照此规范实现底部
3. 现有页面需要逐步迁移到此标准
4. 任何样式修改需要更新此文档

---

**注意**：此规范确保CRM系统所有页面底部的一致性，请严格按照标准执行。如需修改，请先更新此规范文档。