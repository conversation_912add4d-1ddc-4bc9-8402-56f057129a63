package com.ews;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.exceptions.WebsocketNotConnectedException;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSON;
import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.TradeAccount;
/**
 * MT接口调用DEMO
 * <AUTHOR>
 */
public class Test3 {
	
	public static void main(String args[])throws Exception{
		
		try {
		    URI uri=new URI("ws://127.0.0.1:8090");
		    WebSocketClient myClient = new WebSocketClient(uri){
            @Override
            public void onOpen(ServerHandshake serverHandshake) {
            }
            @Override
            public void onMessage(String s) {
             System.out.println(s);
            }
            @Override
            public void onClose(int i, String s, boolean b) {
            }
            @Override
            public void onError(Exception e) {
            	 System.out.println(e);
            }
           
        };
        myClient.connect();
				
		HashMap map=new HashMap();
		
		/**/
		//注册新用户
		map.put("reqtype", "register");
		map.put("reqid", String.valueOf(new Date().getTime()));
		map.put("login", ********);              //可以指定交易账户的ID,填0则自动从MT系统获取
		map.put("username", "开户测试");
		map.put("leverage", 100);           //杠杆
		map.put("groupname", "OTE-MM-3721");    //交易组
		map.put("password", "qq111111");
		map.put("investor", "qq222222");
		map.put("phonepwd", "qq333333");
		
		
		
		/*
		//查询用户信息
		map.put("reqtype", "getuserinfo");
		map.put("login", 65500465);
		map.put("reqid", new Date().getTime());
		*/
		
		/*   
		//出入金 及信用增减 
		map.put("reqtype", "deposituserinfo");
		map.put("login", 49001);
		map.put("reqid", String.valueOf(new Date().getTime()));
		map.put("operationtype", 1);         //1 表示入金，2表示出金 ,3 增加信用，4 减少信用
		map.put("amount", 10D);
		//map.put("endtime", 1867656947);    //有效期  一般不要赋值
		map.put("comment", "test");
	*/
		
		/*
		//当前持仓
		map.put("reqtype", "ordersuserinfo");
		map.put("login",4121);
		map.put("reqid", new Date().getTime());
	    */
		
		/*
		 //历史订单
		
		map.put("reqtype", "historyorderinfo");
		map.put("login", 65500465);
		map.put("reqid", new Date().getTime());
		//map.put("symbol", "");
	  //  map.put("tradeType",0);                 //0 buy  1 sell   6  出入金    7 信用
		map.put("fromtime", 1732106523);
		map.put("endtime", 1732797723);           //查询加1-8个时区
		
		*/
		
		
		/*
		//资金情况
		map.put("reqtype", "marginleveluserinfo");
		map.put("reqid", new Date().getTime());
		map.put("count", 2);
			
		HashMap mm1=new HashMap();
		mm1.put("login", 13056185);
		
		HashMap mm2=new HashMap();
		mm2.put("login", 1278193);
		
		java.util.List list2=new ArrayList();
		list2.add(mm1);
		list2.add(mm2);
		map.put("loginItem", list2);
		
		map.put("groupname", "YG-MM10");
		System.out.println(JSON.toJSONString(map));
		*/
		
		/* 
		//开仓 
		map.put("reqtype", "tradeorderinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 100015);
		map.put("symbol", "EURUSD.s");
		map.put("tradetype", 66);            //按市价开仓
		map.put("tradecmd",0);                 //   0 buy  1 sell
		//map.put("price", 1.09234);
		map.put("volume", 10);
		map.put("sl", 0);
		map.put("tp", 0);
		map.put("order", 0);
		map.put("orderby", 6022);
		map.put("comment", "API下单");
    
		 */
		/* 
		//平仓
		map.put("reqtype", "tradeorderinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 100015);
		map.put("symbol", "EURUSD.s");        //交易品种
		map.put("tradetype", 70);             //70按市价平仓
		map.put("tradecmd",0);                 //   0 buy  1 sell
		//map.put("price", 1.09234);
		map.put("volume", 1);           
		map.put("sl", 0);                 
		map.put("tp", 0);
	//	map.put("order", 501279);    //要平仓的订单号
		map.put("orderby", 6022);     //操作人账号
		map.put("comment", "test平仓");
		*/
		
		
		/*
		//一键平仓
		map.put("reqtype", "closeallordersinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 2011);
		*/
		
		
		//更改账号信息
		/*
		map.put("reqtype", "updateuserinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 4107);
		map.put("username", "张大强");
		map.put("email", "<EMAIL>");
		map.put("country", "China");
		map.put("state", "北京");
		map.put("city","北京");
		map.put("address", "三里屯");
		map.put("phone", "186");
		map.put("comment", "测试账号");
		map.put("enable", 0);//是否启用  1 启用  0 禁用
		map.put("leverage", 500);  
		map.put("groupname", "YG-MM10");
		*/
		/*
		//查询交易对	
		map.put("reqtype", "getsysmbollistinfo");
		map.put("reqid", new Date().getTime());
	    */
		
		
		/*
		 //获得组记录
		map.put("reqtype", "GetGroupsName");
		map.put("reqid", String.valueOf(new Date().getTime()));
		
		 */
		/*
		//根据组同步获取交易账号列表
		map.put("reqtype", "GetUserListByGroup");
		map.put("groupname", "YG-MM00");
		map.put("reqid", new Date().getTime());
		
		*/
		
		
		/*
		//修改历史订单
		map.put("reqtype", "modHistoryRecord");
		map.put("reqid", new Date().getTime());
		map.put("order", 510154);
		map.put("open_time", 1585130411);
		map.put("close_time", 1585130422);
		map.put("open_price", 1.084329);
		map.put("close_price",  1.08422);
		map.put("profit", 11.1);
		*/
		
		
		/*
		//根据manager的ID查所有的交易账号
		map.put("reqtype", "getuserlistinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 6019);
		*/
		/*
		//根据manager的ID查所有的交易账号和状态(仅查这两项)
		map.put("reqtype", "getuserlistsimpleinfo");
		map.put("reqid", new Date().getTime());
		map.put("login", 6008);
		*/
		
		
		/*
		//修改交易账号密码
		    map.put("reqtype", "resetuserpswinfo");
			map.put("reqid", new Date().getTime());
			map.put("login", "4108");
			map.put("type", 1); // 1为主交易密码  2为投资者密码 3 校验主密码
			map.put("oldpassword", "T12345678");
			map.put("newpassword", "T88888888");
			
			*/
			
			//验证密码
		
		/*
			map.put("reqtype", "resetuserpswinfo");
			map.put("reqid", new Date().getTime());
			map.put("login", ********);
			map.put("type", 3); // 1为主交易密码  2为投资者密码 3 校验主密码
			map.put("oldpassword", "VDGGUVS774");
			*/
		
		
		   //验证失败信息 --- "error" : "Invalid account",
			
			//成功信息
			
			//{
			 //  "error" : "",
			 //  "login" : 1002,
			  // "reqid" : "*************",
			  // "status" : 1
			//}
		 
			
		
	
		
		
        JSONObject jsonObj=new JSONObject(map);
        
      
		myClient.send(jsonObj.toString());
      
        
		//myClient.close();
		}catch(WebsocketNotConnectedException e) {
			System.out.println(e);
			
			
		}
		
		
		/*消息推送信息--用户信息
		 {
			   "MsgType" : "UserInfo",
			   "address" : "",
			   "agent_account" : 0,
			   "balance" : "0.00",
			   "city" : "",
			   "comment" : "",
			   "country" : "",
			   "credit" : "0.00",
			   "email" : "",
			   "enable" : 1,
			   "enable_change_password" : 1,
			   "enable_read_only" : 0,
			   "groupname" : "test-c010",
			   "id" : "",
			   "interestrate" : 0,
			   "leverage" : 100,
			   "login" : 1001,
			   "password_investor" : "",
			   "password_phone" : "T12345699",
			   "phone" : "",
			   "regdate" : **********,
			   "send_reports" : 0,
			   "state" : "",
			   "taxes" : 0,
			   "username" : "中文",
			   "userstatus" : "RE",
			   "zipcode" : ""
			}
		 
		 */
		
		/*消息推送-交易信息
		 {
			   "MsgType" : "Trade",
			   "OrderType" : 1,
			   "activation" : 0,
			   "api_data" : true,
			   "close_price" : 0,
			   "close_time" : **********,
			   "cmd" : 6,
			   "comment" : "test",
			   "commission" : 0,
			   "commission_agent" : 0,
			   "conv_rate1" : 0,
			   "conv_rate2" : 0,
			   "conv_reserv" : "",
			   "digits" : 0,
			   "expiration" : 0,
			   "gw_close_price" : 0,
			   "gw_open_price" : 0,
			   "gw_order" : 0,
			   "gw_volume" : 0,
			   "login" : 1001,
			   "magic" : 0,
			   "margin_rate" : 0,
			   "open_price" : 0,
			   "open_time" : **********,
			   "order" : 3074,
			   "profit" : 10,
			   "reason" : 0,
			   "sl" : 0,
			   "state" : 0,
			   "storage" : 0,
			   "symbol" : "",
			   "taxes" : 0,
			   "timestamp" : **********,
			   "tp" : 0,
			   "volume" : 1
			}
		 */

		
	}

}
