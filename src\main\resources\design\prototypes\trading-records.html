<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USK外汇CRM - 交易记录</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(26, 31, 46, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.2);
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            overflow: hidden;
        }

        .nav-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .nav-title {
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-left: 50px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #007AFF;
            background: rgba(0, 122, 255, 0.1);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 主要内容区域 */
        .main-container {
            padding: 30px;
            max-width: 1600px;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
        }

        /* 统计概览区域 */
        .stats-overview {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 20px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: rgba(0, 122, 255, 0.4);
            transform: translateY(-2px);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #00FF88);
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 12px;
        }

        .stat-title {
            color: rgba(255, 255, 255, 0.7);
            font-size: 13px;
            font-weight: 500;
        }

        .stat-icon {
            font-size: 18px;
        }

        .stat-value {
            font-size: 24px;
            font-weight: 700;
            color: #ffffff;
            font-family: 'Courier New', monospace;
            margin-bottom: 6px;
        }

        .stat-change {
            font-size: 12px;
            display: flex;
            align-items: center;
            gap: 4px;
        }

        .change-positive { color: #00FF88; }
        .change-negative { color: #FF3B30; }

        /* 筛选和操作区域 */
        .filters-section {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 25px;
            margin-bottom: 20px;
        }

        .filters-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .filters-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .filter-actions {
            display: flex;
            gap: 15px;
        }

        .action-btn {
            padding: 10px 20px;
            border: 1px solid rgba(0, 122, 255, 0.3);
            border-radius: 8px;
            background: transparent;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            cursor: pointer;
            transition: all 0.3s ease;
            display: flex;
            align-items: center;
            gap: 8px;
        }

        .action-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        .action-btn.primary {
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-color: transparent;
            color: #ffffff;
        }

        .action-btn.primary:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(0, 122, 255, 0.4);
        }

        /* 筛选表单 */
        .filters-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 20px;
            align-items: end;
        }

        .filter-group {
            display: flex;
            flex-direction: column;
        }

        .filter-label {
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .filter-input, .filter-select {
            padding: 12px 16px;
            background: rgba(45, 58, 79, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            color: #ffffff;
            font-size: 14px;
            transition: all 0.3s ease;
        }

        .filter-input:focus, .filter-select:focus {
            outline: none;
            border-color: #007AFF;
            background: rgba(45, 58, 79, 0.8);
            box-shadow: 0 0 15px rgba(0, 122, 255, 0.2);
        }

        .filter-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* 数据表格区域 */
        .table-section {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            overflow: hidden;
            margin-bottom: 30px;
        }

        .table-header {
            padding: 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .table-title {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .table-controls {
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .records-count {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        .pagination-info {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        /* 表格样式 */
        .data-table {
            width: 100%;
            border-collapse: collapse;
        }

        .data-table th {
            background: rgba(0, 0, 0, 0.2);
            color: rgba(255, 255, 255, 0.8);
            padding: 15px 20px;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            position: sticky;
            top: 0;
            z-index: 10;
        }

        .data-table th:hover {
            background: rgba(0, 122, 255, 0.1);
            cursor: pointer;
        }

        .data-table td {
            padding: 18px 20px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            font-size: 14px;
            vertical-align: middle;
        }

        .data-table tr:hover {
            background: rgba(0, 122, 255, 0.05);
        }

        .order-id {
            font-family: 'Courier New', monospace;
            color: #007AFF;
            font-weight: 600;
        }

        .account-info {
            display: flex;
            flex-direction: column;
            gap: 2px;
        }

        .account-name {
            color: #ffffff;
            font-weight: 500;
        }

        .account-type {
            color: rgba(255, 255, 255, 0.5);
            font-size: 12px;
        }

        .trade-amount {
            font-family: 'Courier New', monospace;
            font-weight: 600;
        }

        .amount-positive { color: #00FF88; }
        .amount-negative { color: #FF3B30; }

        .trade-type {
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .type-buy {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
        }

        .type-sell {
            background: rgba(255, 59, 48, 0.2);
            color: #FF3B30;
        }

        .currency-pair {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #ffffff;
        }

        .trade-status {
            display: flex;
            align-items: center;
            gap: 6px;
        }

        .status-dot {
            width: 8px;
            height: 8px;
            border-radius: 50%;
        }

        .status-completed .status-dot { background: #00FF88; }
        .status-pending .status-dot { background: #FF9500; }
        .status-failed .status-dot { background: #FF3B30; }

        /* 分页控件 */
        .pagination {
            display: flex;
            justify-content: center;
            align-items: center;
            gap: 10px;
            padding: 20px;
            background: rgba(26, 31, 46, 0.8);
            border-radius: 12px;
            border: 1px solid rgba(0, 122, 255, 0.15);
        }

        .pagination-btn {
            padding: 8px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .pagination-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        .pagination-btn.active {
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-color: transparent;
            color: #ffffff;
        }

        .pagination-btn:disabled {
            opacity: 0.3;
            cursor: not-allowed;
        }

        /* 空状态 */
        .empty-state {
            text-align: center;
            padding: 60px 20px;
            color: rgba(255, 255, 255, 0.6);
        }

        .empty-icon {
            font-size: 64px;
            margin-bottom: 20px;
            opacity: 0.5;
        }

        .empty-title {
            font-size: 18px;
            font-weight: 500;
            margin-bottom: 8px;
            color: rgba(255, 255, 255, 0.8);
        }

        .empty-desc {
            font-size: 14px;
            color: rgba(255, 255, 255, 0.5);
        }

        /* 加载状态 */
        .loading-overlay {
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            bottom: 0;
            background: rgba(26, 31, 46, 0.8);
            display: flex;
            align-items: center;
            justify-content: center;
            backdrop-filter: blur(5px);
            z-index: 1000;
        }

        .loading-spinner {
            width: 40px;
            height: 40px;
            border: 3px solid rgba(0, 122, 255, 0.3);
            border-top: 3px solid #007AFF;
            border-radius: 50%;
            animation: spin 1s linear infinite;
        }

        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .stats-overview {
                grid-template-columns: repeat(auto-fit, minmax(180px, 1fr));
            }
            
            .filters-grid {
                grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
            }
            
            .table-section {
                overflow-x: auto;
            }
            
            .data-table {
                min-width: 800px;
            }
            
            .nav-menu {
                display: none;
            }
            
            .filter-actions {
                flex-direction: column;
                gap: 10px;
            }
            
            .action-btn {
                width: 100%;
                justify-content: center;
            }
        }

        /* 表格滚动条 */
        .table-container {
            max-height: 600px;
            overflow-y: auto;
            position: relative;
        }

        .table-container::-webkit-scrollbar {
            width: 8px;
        }

        .table-container::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb {
            background: rgba(0, 122, 255, 0.5);
            border-radius: 4px;
        }

        .table-container::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 122, 255, 0.7);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-left">
            <div class="nav-logo"><img src="logo.png" alt="USK"></div>
            <div class="nav-title">USK FOREX CRM</div>
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-item">账户总览</a>
                <a href="fund-management.html" class="nav-item">资金管理</a>
                <a href="trading-records.html" class="nav-item active">交易记录</a>
                <a href="profile.html" class="nav-item">个人信息</a>
            </div>
        </div>
        <div class="nav-right">
            <div class="nav-user">
                <div class="user-avatar">张</div>
                <span>张先生</span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">交易记录</h1>
            <p class="page-subtitle">查询和分析您的交易历史记录</p>
        </div>

        <!-- 统计概览 -->
        <div class="stats-overview">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总交易笔数</span>
                    <span class="stat-icon">📊</span>
                </div>
                <div class="stat-value">1,247</div>
               
            </div>

           

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总盈亏</span>
                    <span class="stat-icon">📈</span>
                </div>
                <div class="stat-value">+$145,678</div>
               
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">可能余额</span>
                    <span class="stat-icon">💰</span>
                </div>
                <div class="stat-value">$2.8M</div>
               
            </div>

         

          
        </div>

        <!-- 筛选和操作区域 -->
        <div class="filters-section">
            <div class="filters-header">
                <h3 class="filters-title">
                    🔍 高级筛选
                </h3>
                <div class="filter-actions">
                    <button class="action-btn" onclick="resetFilters()">
                        🔄 重置筛选
                    </button>
                    <button class="action-btn" onclick="exportData()">
                        📊 导出Excel
                    </button>
                    <button class="action-btn primary" onclick="searchRecords()">
                        🔍 查询记录
                    </button>
                </div>
            </div>

            <div class="filters-grid">
                <div class="filter-group">
                    <label class="filter-label">开始日期</label>
                    <input type="date" class="filter-input" id="start-date" value="2025-01-01">
                </div>
                <div class="filter-group">
                    <label class="filter-label">结束日期</label>
                    <input type="date" class="filter-input" id="end-date" value="2025-01-30">
                </div>
                <div class="filter-group">
                    <label class="filter-label">交易账户</label>
                    <select class="filter-select" id="account-filter">
                        <option value="">全部账户</option>
                        <option value="MT5-********">MT5-********</option>
                        <option value="MT5-********">MT5-********</option>
                        <option value="MT5-********">MT5-********</option>
                        <option value="MT5-********">MT5-********</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">货币对</label>
                    <select class="filter-select" id="symbol-filter">
                        <option value="">全部货币对</option>
                        <option value="EURUSD">EUR/USD</option>
                        <option value="GBPUSD">GBP/USD</option>
                        <option value="USDJPY">USD/JPY</option>
                        <option value="AUDUSD">AUD/USD</option>
                        <option value="USDCAD">USD/CAD</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">交易方向</label>
                    <select class="filter-select" id="direction-filter">
                        <option value="">全部方向</option>
                        <option value="buy">买入 (Buy)</option>
                        <option value="sell">卖出 (Sell)</option>
                    </select>
                </div>
                <div class="filter-group">
                    <label class="filter-label">订单号</label>
                    <input type="text" class="filter-input" placeholder="输入订单号搜索" id="order-search">
                </div>
            </div>
        </div>

        <!-- 数据表格 -->
        <div class="table-section">
            <div class="table-header">
                <h3 class="table-title">
                    📋 交易记录详情
                </h3>
                <div class="table-controls">
                    <span class="records-count">共找到 <strong>1,247</strong> 条记录</span>
                    <span class="pagination-info">显示 1-50 条</span>
                </div>
            </div>

            <div class="table-container" id="table-container">
                <table class="data-table">
                    <thead>
                        <tr>
                            <th onclick="sortTable('orderId')">订单号 ↕️</th>
                            <th onclick="sortTable('account')">交易账户</th>
                            <th onclick="sortTable('time')">交易时间 ↕️</th>
                            <th onclick="sortTable('symbol')">货币对</th>
                            <th onclick="sortTable('type')">交易方向</th>
                            <th onclick="sortTable('volume')">手数</th>
                            <th onclick="sortTable('openPrice')">开仓价格</th>
                            <th onclick="sortTable('closePrice')">平仓价格</th>
                            <th onclick="sortTable('profit')">盈亏 ↕️</th>
                            <th onclick="sortTable('status')">状态</th>
                        </tr>
                    </thead>
                    <tbody id="records-tbody">
                        <!-- 交易记录数据 -->
                        <tr>
                            <td class="order-id">#**********</td>
                            <td>
                                <div class="account-info">
                                    <span class="account-name">MT5-********</span>
                                    <span class="account-type">标准账户</span>
                                </div>
                            </td>
                            <td>2025-01-30 14:25:33</td>
                            <td class="currency-pair">EUR/USD</td>
                            <td><span class="trade-type type-buy">买入</span></td>
                            <td>1.5</td>
                            <td>1.0856</td>
                            <td>1.0889</td>
                            <td class="trade-amount amount-positive">+$495.00</td>
                            <td>
                                <div class="trade-status status-completed">
                                    <span class="status-dot"></span>
                                    <span>已完成</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="order-id">#**********</td>
                            <td>
                                <div class="account-info">
                                    <span class="account-name">MT5-********</span>
                                    <span class="account-type">ECN账户</span>
                                </div>
                            </td>
                            <td>2025-01-30 13:45:21</td>
                            <td class="currency-pair">GBP/USD</td>
                            <td><span class="trade-type type-sell">卖出</span></td>
                            <td>2.0</td>
                            <td>1.2734</td>
                            <td>1.2698</td>
                            <td class="trade-amount amount-positive">+$720.00</td>
                            <td>
                                <div class="trade-status status-completed">
                                    <span class="status-dot"></span>
                                    <span>已完成</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="order-id">#**********</td>
                            <td>
                                <div class="account-info">
                                    <span class="account-name">MT5-********</span>
                                    <span class="account-type">标准账户</span>
                                </div>
                            </td>
                            <td>2025-01-30 12:15:08</td>
                            <td class="currency-pair">USD/JPY</td>
                            <td><span class="trade-type type-buy">买入</span></td>
                            <td>1.0</td>
                            <td>149.82</td>
                            <td>149.45</td>
                            <td class="trade-amount amount-negative">-$247.00</td>
                            <td>
                                <div class="trade-status status-completed">
                                    <span class="status-dot"></span>
                                    <span>已完成</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="order-id">#**********</td>
                            <td>
                                <div class="account-info">
                                    <span class="account-name">MT5-********</span>
                                    <span class="account-type">迷你账户</span>
                                </div>
                            </td>
                            <td>2025-01-30 11:30:42</td>
                            <td class="currency-pair">AUD/USD</td>
                            <td><span class="trade-type type-sell">卖出</span></td>
                            <td>0.5</td>
                            <td>0.6623</td>
                            <td>0.6635</td>
                            <td class="trade-amount amount-negative">-$60.00</td>
                            <td>
                                <div class="trade-status status-completed">
                                    <span class="status-dot"></span>
                                    <span>已完成</span>
                                </div>
                            </td>
                        </tr>
                        <tr>
                            <td class="order-id">#**********</td>
                            <td>
                                <div class="account-info">
                                    <span class="account-name">MT5-********</span>
                                    <span class="account-type">VIP账户</span>
                                </div>
                            </td>
                            <td>2025-01-30 10:45:15</td>
                            <td class="currency-pair">EUR/USD</td>
                            <td><span class="trade-type type-buy">买入</span></td>
                            <td>3.0</td>
                            <td>1.0845</td>
                            <td>-</td>
                            <td class="trade-amount">+$125.00</td>
                            <td>
                                <div class="trade-status status-pending">
                                    <span class="status-dot"></span>
                                    <span>进行中</span>
                                </div>
                            </td>
                        </tr>
                    </tbody>
                </table>
            </div>
        </div>

        <!-- 分页控件 -->
        <div class="pagination">
            <button class="pagination-btn" onclick="goToPage('first')" disabled>首页</button>
            <button class="pagination-btn" onclick="goToPage('prev')" disabled>上一页</button>
            <button class="pagination-btn active">1</button>
            <button class="pagination-btn" onclick="goToPage(2)">2</button>
            <button class="pagination-btn" onclick="goToPage(3)">3</button>
            <button class="pagination-btn" onclick="goToPage(4)">4</button>
            <button class="pagination-btn" onclick="goToPage(5)">5</button>
            <span style="color: rgba(255,255,255,0.5); padding: 0 10px;">...</span>
            <button class="pagination-btn" onclick="goToPage(25)">25</button>
            <button class="pagination-btn" onclick="goToPage('next')">下一页</button>
            <button class="pagination-btn" onclick="goToPage('last')">末页</button>
        </div>
    </div>

    <script>
        let currentSort = { column: null, direction: 'asc' };
        let currentPage = 1;
        let recordsPerPage = 50;

        // 排序功能
        function sortTable(column) {
            if (currentSort.column === column) {
                currentSort.direction = currentSort.direction === 'asc' ? 'desc' : 'asc';
            } else {
                currentSort.column = column;
                currentSort.direction = 'asc';
            }
            
            // 这里应该发送请求到后端进行排序
            console.log(`排序: ${column} ${currentSort.direction}`);
            
            // 模拟排序效果
            showLoading();
            setTimeout(() => {
                hideLoading();
                updateSortIcons();
            }, 1000);
        }

        // 更新排序图标
        function updateSortIcons() {
            const headers = document.querySelectorAll('th');
            headers.forEach(th => {
                const text = th.textContent;
                if (text.includes('↕️') || text.includes('↑') || text.includes('↓')) {
                    th.textContent = text.replace(/[↕️↑↓]/g, '') + '↕️';
                }
            });
            
            if (currentSort.column) {
                const targetHeader = Array.from(headers).find(th => 
                    th.textContent.toLowerCase().includes(currentSort.column.toLowerCase())
                );
                if (targetHeader) {
                    const text = targetHeader.textContent.replace(/[↕️↑↓]/g, '');
                    targetHeader.textContent = text + (currentSort.direction === 'asc' ? '↑' : '↓');
                }
            }
        }

        // 搜索记录
        function searchRecords() {
            const filters = {
                startDate: document.getElementById('start-date').value,
                endDate: document.getElementById('end-date').value,
                account: document.getElementById('account-filter').value,
                symbol: document.getElementById('symbol-filter').value,
                direction: document.getElementById('direction-filter').value,
                orderSearch: document.getElementById('order-search').value
            };
            
            console.log('搜索条件:', filters);
            
            showLoading();
            setTimeout(() => {
                hideLoading();
                // 模拟搜索结果更新
                updateRecordsCount(Math.floor(Math.random() * 500) + 100);
            }, 1500);
        }

        // 重置筛选
        function resetFilters() {
            document.getElementById('start-date').value = '2025-01-01';
            document.getElementById('end-date').value = '2025-01-30';
            document.getElementById('account-filter').value = '';
            document.getElementById('symbol-filter').value = '';
            document.getElementById('direction-filter').value = '';
            document.getElementById('order-search').value = '';
            
            searchRecords();
        }

        // 导出数据
        function exportData() {
            const btn = event.target;
            const originalText = btn.innerHTML;
            
            btn.innerHTML = '📊 导出中...';
            btn.disabled = true;
            
            setTimeout(() => {
                btn.innerHTML = '✅ 导出成功';
                setTimeout(() => {
                    btn.innerHTML = originalText;
                    btn.disabled = false;
                }, 2000);
                
                // 模拟文件下载
                const link = document.createElement('a');
                link.href = 'data:text/plain;charset=utf-8,交易记录导出文件';
                link.download = `交易记录_${new Date().toISOString().slice(0, 10)}.csv`;
                link.click();
            }, 2000);
        }

        // 分页功能
        function goToPage(page) {
            if (page === 'first') page = 1;
            if (page === 'last') page = 25;
            if (page === 'prev') page = Math.max(1, currentPage - 1);
            if (page === 'next') page = Math.min(25, currentPage + 1);
            
            currentPage = page;
            
            // 更新分页按钮状态
            document.querySelectorAll('.pagination-btn').forEach(btn => {
                btn.classList.remove('active');
                if (btn.textContent == page) {
                    btn.classList.add('active');
                }
            });
            
            showLoading();
            setTimeout(() => {
                hideLoading();
                updatePaginationInfo();
            }, 800);
        }

        // 显示加载状态
        function showLoading() {
            const container = document.getElementById('table-container');
            const overlay = document.createElement('div');
            overlay.className = 'loading-overlay';
            overlay.innerHTML = '<div class="loading-spinner"></div>';
            container.style.position = 'relative';
            container.appendChild(overlay);
        }

        // 隐藏加载状态
        function hideLoading() {
            const overlay = document.querySelector('.loading-overlay');
            if (overlay) {
                overlay.remove();
            }
        }

        // 更新记录数量
        function updateRecordsCount(count) {
            document.querySelector('.records-count').innerHTML = 
                `共找到 <strong>${count}</strong> 条记录`;
        }

        // 更新分页信息
        function updatePaginationInfo() {
            const start = (currentPage - 1) * recordsPerPage + 1;
            const end = Math.min(currentPage * recordsPerPage, 1247);
            document.querySelector('.pagination-info').textContent = 
                `显示 ${start}-${end} 条`;
        }

        // 模拟实时数据更新
        function updateStatsData() {
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach((value, index) => {
                if (index < 3) { // 只更新前3个统计数据
                    const currentText = value.textContent;
                    let currentValue = 0;
                    
                    if (currentText.includes('$')) {
                        currentValue = parseFloat(currentText.replace(/[$,KM]/g, ''));
                        if (currentText.includes('K')) currentValue *= 1000;
                        if (currentText.includes('M')) currentValue *= 1000000;
                    } else {
                        currentValue = parseFloat(currentText.replace(/,/g, ''));
                    }
                    
                    const change = (Math.random() - 0.5) * currentValue * 0.01;
                    const newValue = currentValue + change;
                    
                    if (currentText.includes('$')) {
                        if (newValue >= 1000000) {
                            value.textContent = '$' + (newValue / 1000000).toFixed(1) + 'M';
                        } else if (newValue >= 1000) {
                            value.textContent = '$' + (newValue / 1000).toFixed(0) + 'K';
                        } else {
                            value.textContent = '$' + newValue.toFixed(0);
                        }
                    } else {
                        value.textContent = Math.floor(newValue).toLocaleString();
                    }
                }
            });
        }

        // 页面初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 每60秒更新一次统计数据
            setInterval(updateStatsData, 60000);
            
            // 初始化日期范围
            const today = new Date();
            const thirtyDaysAgo = new Date(today.getTime() - 30 * 24 * 60 * 60 * 1000);
            
            document.getElementById('end-date').value = today.toISOString().slice(0, 10);
            document.getElementById('start-date').value = thirtyDaysAgo.toISOString().slice(0, 10);
        });

        // 表格行点击事件
        document.addEventListener('click', function(e) {
            const row = e.target.closest('tbody tr');
            if (row) {
                const orderId = row.querySelector('.order-id').textContent;
                console.log('查看订单详情:', orderId);
                // 这里可以打开订单详情弹窗
            }
        });
    </script>
</body>
</html> 