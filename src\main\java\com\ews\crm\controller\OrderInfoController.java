
package com.ews.crm.controller;

import java.io.File;
import java.math.BigDecimal;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.shiro.SecurityUtils;
import org.apache.shiro.authz.annotation.RequiresPermissions;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import com.ews.common.EncryptUtils;
import com.ews.common.DateUtil;
import com.ews.common.LoginResult;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.common.Result;

import com.alibaba.fastjson.JSONObject; 
import com.alibaba.fastjson.JSONArray;


import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserInfo;



@RestController
@RequestMapping("/admin/orderInfo")
public class OrderInfoController {
	@Autowired
	private OrderInfoService orderInfoService;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private TradeAccountService tradeAccountService;



   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	OrderInfo query  = new OrderInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("orderNo"))) {
               	query.setOrderNo(request.getParameter("orderNo").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("type"))) {
               	query.setType(Integer.parseInt(request.getParameter("type").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("symbol"))) {
               	query.setSymbol(request.getParameter("symbol").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("status"))) {
               	query.setStatus(Integer.parseInt(request.getParameter("status").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(Long.parseLong(request.getParameter("tradeId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("loginId"))) {
               	query.setLoginId(request.getParameter("loginId").trim());
        	}
        	Page<OrderInfo> pages = orderInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<OrderInfo> orderInfos = pages.getContent();
        	for(int i=0;i<orderInfos.size();i++) {
        		OrderInfo entity  = orderInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}
        	
        	datas.put("items", orderInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}

	 /**
	* 我的订单
	* @param request
	* @return
	*/
    @PostMapping("/mylist")
    public ResponseData mylist(HttpServletRequest request) {
    	try {
        	OrderInfo query  = new OrderInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("orderNo"))) {
               	query.setOrderNo(request.getParameter("orderNo").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("type"))) {
               	query.setType(Integer.parseInt(request.getParameter("type").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("symbol"))) {
               	query.setSymbol(request.getParameter("symbol").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("status"))) {
               	query.setStatus(2);
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(Long.parseLong(request.getParameter("tradeId").trim())) ;
        	}
			if(!StringUtils.isEmpty(request.getParameter("closeTime1"))) {
				query.setQueryBeginTime(Integer.parseInt(request.getParameter("closeTime1").trim()));
			}else{
				query.setQueryBeginTime(Integer.parseInt(String.valueOf(new Date().getTime()/1000-(60*60*24*60))));
			}
			if(!StringUtils.isEmpty(request.getParameter("closeTime2"))) {
				query.setQueryEndTime(Integer.parseInt(request.getParameter("closeTime2").trim()));
			}		
        	
			
			User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
			if(loginUser!=null) {
				
				User uu=(User)this.userService.findUserById(loginUser.getUserId());
				
				if(uu!=null&&uu.getUserId()!=null&&uu.getStoreId()!=null) {
					if(uu.getStoreId()!=null) {
						query.setLoginId(uu.getStoreId().toString());
					}
				}else {
					query.setLoginId("*********") ;
				}
			}else {
				query.setLoginId("*********") ;
			}

			List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	
	    	query.setTypeList(ll);
        	Page<OrderInfo> pages = orderInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<OrderInfo> orderInfos = pages.getContent();
        	for(int i=0;i<orderInfos.size();i++) {
        		OrderInfo entity  = orderInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}

			Page<OrderInfo> pages2 = orderInfoService.findAll(0, 200000, sort, sortBy, query);
        	    double yjss=0d;
				double hlje=0d;
	            	for(int m=0;m<pages2.getContent().size();m++) {
	            		OrderInfo oii=(OrderInfo)pages2.getContent().get(m);
	            		BigDecimal bg = new BigDecimal(oii.getVolume());
	            		double f1 = bg.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
	            		yjss=yjss+f1;

						BigDecimal bg2=new BigDecimal(oii.getProfit());
						double f2=bg2.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
						hlje=hlje+f2;
	            	}
					DecimalFormat df3=new DecimalFormat("##0.00");
					DecimalFormat df4=new DecimalFormat("##0.00");
	            	datas.put("yjss", df3.format(yjss));
					datas.put("hlje", df4.format(hlje));
        	datas.put("items", orderInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}

	 /**
	* 团队订单
	* @param request
	* @return
	*/
    @PostMapping("/teamlist")
    public ResponseData teamlist(HttpServletRequest request) {
    	try {
        	OrderInfo query  = new OrderInfo();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
        	if (!StringUtils.isEmpty(request.getParameter("orderNo"))) {
               	query.setOrderNo(request.getParameter("orderNo").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("type"))) {
               	query.setType(Integer.parseInt(request.getParameter("type").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("symbol"))) {
               	query.setSymbol(request.getParameter("symbol").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("status"))) {
               	query.setStatus(2);
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(Long.parseLong(request.getParameter("tradeId").trim())) ;
        	}
			if(!StringUtils.isEmpty(request.getParameter("closeTime1"))) {
				query.setQueryBeginTime(Integer.parseInt(request.getParameter("closeTime1").trim()));
			}else{
				query.setQueryBeginTime(Integer.parseInt(String.valueOf(new Date().getTime()/1000-(60*60*24*60))));
			}
			if(!StringUtils.isEmpty(request.getParameter("closeTime2"))) {
				query.setQueryEndTime(Integer.parseInt(request.getParameter("closeTime2").trim()));
			}	
        	
			
			User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
			

			List ll=new ArrayList();
	    	ll.add(1);
	    	ll.add(0);
	    	
	    	query.setTypeList(ll);

           

			List tradeLoginIdList = new ArrayList();
			User query_user = new User();
			
			if(loginUser!=null) {
				User uu=(User)this.userService.findUserById(loginUser.getUserId());
				if(uu!=null&&uu.getUserId()!=null) {
						if(!StringUtils.isEmpty(request.getParameter("agentAcount"))) {
						User queryUser = this.userService.findByUserName(request.getParameter("agentAcount").trim());
						if(queryUser!=null) {
						//query_user.setUsername(request.getParameter("agentAcount").trim());//查询代理本身
						query_user.setSortStr(queryUser.getSortStr());//查询代理名下所有用户
						}
					}else{
						if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
							//1.查出自己及名下所有的代理
							query_user.setSortStr(uu.getSortStr());
						}
					}
				}
			}else {
				query_user.setSortStr("998828282837748373738");
			}	 
			Page<User> pages_user = userService.findAll(0, 10000, "userId", "asc", query_user);
			List<User> userList = pages_user.getContent();
			
			
			for(int i=0;i<userList.size();i++) {

			
				User uu=(User)userList.get(i);
			
				UserInfo userInfo_query = new UserInfo();
				userInfo_query.setParentId(uu.getUserId());
				Page<UserInfo> pages_userInfo = userInfoService.findAll(0, 10000, sort, sortBy, userInfo_query);
				List<UserInfo> userInfoList = pages_userInfo.getContent();


				for(int j=0;j<userInfoList.size();j++) {
					UserInfo uii=(UserInfo)userInfoList.get(j);
					TradeAccount tradeAccount_query = new TradeAccount();
					tradeAccount_query.setUserId(uii.getId());
					Page<TradeAccount> pages_tradeAccount = tradeAccountService.findAll(0, 10000, sort, sortBy, tradeAccount_query);
					List<TradeAccount> tradeAccountList = pages_tradeAccount.getContent();
					for(int k=0;k<tradeAccountList.size();k++) {
						TradeAccount tii=(TradeAccount)tradeAccountList.get(k);
						tradeLoginIdList.add(tii.getTradeId());
					}

				}
					
				

			}

			if(tradeLoginIdList.size()>0) {
				query.setTradeLoginIdList(tradeLoginIdList);
			}else{
				tradeLoginIdList.add("*********9999999999");
				query.setLoginId("*********") ;
			}

        	Page<OrderInfo> pages = orderInfoService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<OrderInfo> orderInfos = pages.getContent();
        	for(int i=0;i<orderInfos.size();i++) {
        		OrderInfo entity  = orderInfos.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        	}

			Page<OrderInfo> pages2 = orderInfoService.findAll(0, 200000, sort, sortBy, query);
        	    double yjss=0d;
				double hlje=0d;
	            	for(int m=0;m<pages2.getContent().size();m++) {
	            		OrderInfo oii=(OrderInfo)pages2.getContent().get(m);
	            		BigDecimal bg = new BigDecimal(oii.getVolume());
	            		double f1 = bg.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
	            		yjss=yjss+f1;

						BigDecimal bg2=new BigDecimal(oii.getProfit());
						double f2=bg2.setScale(4, BigDecimal.ROUND_HALF_UP).doubleValue();
						hlje=hlje+f2;
	            	}
					DecimalFormat df3=new DecimalFormat("##0.00");
					DecimalFormat df4=new DecimalFormat("##0.00");
	            	datas.put("yjss", df3.format(yjss));
					datas.put("hlje", df4.format(hlje));
        	datas.put("items", orderInfos);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			OrderInfo orderInfo = new OrderInfo();
        	orderInfo.setOrderNo(data.getString("orderNo"));
        	orderInfo.setType(data.getInteger("type"));
        	orderInfo.setSymbol(data.getString("symbol"));
        	orderInfo.setStatus(data.getInteger("status"));
        	orderInfo.setTradeId(data.getLong("tradeId"));
        	orderInfo.setLoginId(data.getString("loginId"));
			Result re = orderInfoService.saveOrUpdate(orderInfo);
			if(re.getCode().equals(Result.CODEFAIL)){
				return ResponseDataUtil.buildError("save fail");
			}
			return ResponseDataUtil.buildSuccess(orderInfo);
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			OrderInfo orderInfo = this.orderInfoService.findById(id);
				if (orderInfo != null) {
					orderInfo.setOrderNo(data.getString("orderNo"));
					orderInfo.setType(data.getInteger("type"));
					orderInfo.setSymbol(data.getString("symbol"));
					orderInfo.setStatus(data.getInteger("status"));
					orderInfo.setTradeId(data.getLong("tradeId"));
					orderInfo.setLoginId(data.getString("loginId"));
					Result re = orderInfoService.saveOrUpdate(orderInfo);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				Result result = orderInfoService.removeEntityOfLogicalById(id);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = orderInfoService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}


}

