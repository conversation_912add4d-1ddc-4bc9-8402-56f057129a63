
package com.ews.crm.controller;

import java.io.FileOutputStream;
import java.net.URI;
import java.net.URLDecoder;
import java.text.DecimalFormat;
import java.text.SimpleDateFormat;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;
import java.util.Random;

import javax.servlet.http.HttpServletRequest;
import javax.validation.Valid;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.apache.http.HttpResponse;
import org.apache.http.util.EntityUtils;
import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RestController;

import com.alibaba.fastjson.JSONObject;
import com.ews.HttpClient;
import com.ews.common.MyWebsocketClient4Register;
import com.ews.common.Result;
import com.ews.common.SendCloudAPIV2;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.crm.entity.AccountType;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.entity.EmailInfo;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.AccountTypeService;
import com.ews.crm.service.CompanyInfoService;
import com.ews.crm.service.EmailInfoService;
import com.ews.crm.service.LeverService;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.User;
import com.ews.system.service.LoginService;
import com.ews.system.service.UserService;
import com.wx.core.util.HttpClientUtil;


@RestController
@RequestMapping("/admin/tradeAccount")
public class TradeAccountController {
	@Autowired
	private TradeAccountService tradeAccountService;

	@Autowired
	private ServerSettingService serverSettingService;
	
	@Autowired
	private UserInfoService userInfoService;
	
	@Autowired
	private AccountTypeService accountTypeService;
	
	@Autowired
	private EmailInfoService emailInfoService;
	
	@Autowired
	private CompanyInfoService companyInfoService;


	@Autowired
	private LeverService leverService;
	@Autowired
	private LoginService loginService;
	@Autowired
	private UserService userService;
	
	@Autowired
	private OperLogService operLogService;
	
	
	@Autowired
	private OrderInfoService orderInfoService;
	
	@Autowired
	private ConstantConfig constantConfig;
	
	
   /**
	* 分页
	* @param request
	* @return
	*/
    @PostMapping("/list")
    public ResponseData list(HttpServletRequest request) {
    	try {
        	TradeAccount query  = new TradeAccount();
        	Integer page = 0;
        	Integer limit = 10;
        	if (!StringUtils.isEmpty(request.getParameter("page"))) {
            	page = Integer.parseInt(request.getParameter("page").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
            	limit = Integer.parseInt(request.getParameter("limit").trim());
        	}
	    	String sortBy = "desc";
	    	String sort = "id";
	    	if (request.getParameter("sort")!= null) {
	    		sort = request.getParameter("sort").trim();
	    		if (sort.startsWith("+")) {
	    			sortBy = "asc";
	    		}
	  			sort = sort.replace("+", "").replace("-", "");
	    	}
	    	
	    	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
               	query.setTradeId(request.getParameter("tradeId").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("userAccount"))) {
               	query.setUserAccount(request.getParameter("userAccount").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("type"))) {
               	query.setType(Integer.parseInt(request.getParameter("type").trim()));
        	}
        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
               	query.setGroupName(request.getParameter("groupName").trim());
        	}
        	if (!StringUtils.isEmpty(request.getParameter("leverId"))) {
               	query.setLeverId(Long.parseLong(request.getParameter("leverId").trim())) ;
        	}
        	if (!StringUtils.isEmpty(request.getParameter("tradeStatus"))) {
               	query.setTradeStatus(Integer.parseInt(request.getParameter("tradeStatus").trim()));
        	}
        	query.setIsAvailable(1);
        	 DecimalFormat df = new DecimalFormat(".00");
        	 SimpleDateFormat format =  new SimpleDateFormat("YYYY-MM-dd HH:mm:ss"); 
        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
        	 List userList=new ArrayList();
             if(loginUser!=null) {
             	
            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
            	
            	 if(uu!=null&&uu.getUserId()!=null) {
            		 
            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
            			
            			 //1.查出自己及名下所有的代理
            			 
            			 User user_query=new User();
            			 user_query.setSortStr(uu.getSortStr());
            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
            			
            			 
            			 //2.遍历代理查出所有的crm用户ID
            			 
            			
            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
            			 
            			 UserInfo ui_self_query=new UserInfo();
            			 ui_self_query.setParentId(uu.getUserId());
            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
            			 for(int m=0;m<ui_self.getContent().size();m++) {
            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
            				 userList.add(ui_1.getId());
            				 
            			 }
            			 if(ui_self.getContent().size()<=0) {
            				 userList.add(999999L);
            			 }
            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
            			 
            			 for(int n=0;n<ul.getContent().size();n++) {
            				 User user_1=ul.getContent().get(n);
            				 UserInfo ui_self_query_2=new UserInfo();
            				 ui_self_query_2.setParentId(user_1.getUserId());
	            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
	            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
	            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
	            				 userList.add(ui_2.getId());
	            			 }
            			 }
            			 query.setUserInfoList(userList);
            		 }
            	 }else {
            		 userList.add(999999L);
            		 query.setUserInfoList(userList);
            	 }
            	 
             }else {
            	 userList.add(999999L);
            	 query.setUserInfoList(userList);
             }
             
             
        	Page<TradeAccount> pages = tradeAccountService.findAll(page - 1, limit, sort, sortBy, query);
        	JSONObject datas = new JSONObject();
        	List<TradeAccount> tradeAccounts = pages.getContent();
        	for(int i=0;i<tradeAccounts.size();i++) {
        		TradeAccount entity  = tradeAccounts.get(i);
        		 entity.setSortNum((page - 1) * limit + (i + 1));
        		 
        		 if(entity.getType()!=null) {
        		 AccountType aty=(AccountType)this.accountTypeService.findById(new Long(entity.getType()));
            		entity.setBackup3(aty.getTypeName());
        		 }
        		 if(entity.getUserId()!=null) {
        			 UserInfo userInfo=(UserInfo)this.userInfoService.findById(entity.getUserId());
        			 entity.setUserInfo(userInfo);
        		 }else {
        			 entity.setUserInfo(new UserInfo());
        		 }
        		 
        		 /*去掉 查询交易手数*/
        		OrderInfo order_query=new OrderInfo();
     			order_query.setLoginId(String.valueOf(entity.getTradeId()));
     			order_query.setStatus(2);
     			List ll=new ArrayList();
     	    	ll.add(1);
     	    	ll.add(0);
     	    	//ll.add(2);
     	    	//ll.add(3);
     	    	//ll.add(4);
     	    	//l.add(5);
     	    	order_query.setTypeList(ll);
     	    	
     	    	//if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin2"))) {
     	    		Date date1 = new Date();  
     	    		Date date2 =  new Date();  
     	    		order_query.setQueryBeginTime(new Integer(String.valueOf(date1.getTime()/1000))-(60*60*24*30));
     	    		order_query.setQueryEndTime(new Integer(String.valueOf((date2.getTime()/1000)+86399)));
            	//}
     	    	
     			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,100000, "id","asc", order_query);
     			
     			if(oi_page.getContent().size()<=0) {
     				entity.setVolume(0d);
     			}
     			for(int mmm=0;mmm<oi_page.getContent().size();mmm++) {
     				OrderInfo oiii=(OrderInfo)oi_page.getContent().get(mmm);
     				if(entity.getVolume()==null) {
     					entity.setVolume(new Double(df.format(oiii.getVolume())));
     				}else {
     					entity.setVolume(new Double(df.format(entity.getVolume()+oiii.getVolume())));
     				}
     			}
     			
        	}
        	
        	
        	datas.put("items", tradeAccounts);
        	datas.put("total", pages.getTotalElements());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
    
    /**
   	* 分页2
   	* @param request
   	* @return
   	*/
       @PostMapping("/list2")
       public ResponseData list2(HttpServletRequest request) {
       	try {
           	TradeAccount query  = new TradeAccount();
           	Integer page = 0;
           	Integer limit = 10;
           	if (!StringUtils.isEmpty(request.getParameter("page"))) {
               	page = Integer.parseInt(request.getParameter("page").trim());
           	}
           	if (!StringUtils.isEmpty(request.getParameter("limit"))) {
               	limit = Integer.parseInt(request.getParameter("limit").trim());
           	}
   	    	String sortBy = "desc";
   	    	String sort = "id";
   	    	if (request.getParameter("sort")!= null) {
   	    		sort = request.getParameter("sort").trim();
   	    		if (sort.startsWith("+")) {
   	    			sortBy = "asc";
   	    		}
   	  			sort = sort.replace("+", "").replace("-", "");
   	    	}
           	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
                  	query.setTradeId(request.getParameter("tradeId").trim());
           	}
           	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
                  	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
           	}
           	if (!StringUtils.isEmpty(request.getParameter("userAccount"))) {
                  	query.setUserAccount(request.getParameter("userAccount").trim());
           	}
           	if (!StringUtils.isEmpty(request.getParameter("type"))) {
                  	query.setType(Integer.parseInt(request.getParameter("type").trim()));
           	}
           	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
                  	query.setGroupName(request.getParameter("groupName").trim());
           	}
           	if (!StringUtils.isEmpty(request.getParameter("leverId"))) {
                  	query.setLeverId(Long.parseLong(request.getParameter("leverId").trim())) ;
           	}
           	if (!StringUtils.isEmpty(request.getParameter("tradeStatus"))) {
                  	query.setTradeStatus(Integer.parseInt(request.getParameter("tradeStatus").trim()));
           	}
           	query.setIsAvailable(0);
           	Page<TradeAccount> pages = tradeAccountService.findAll(page - 1, limit, sort, sortBy, query);
           	JSONObject datas = new JSONObject();
           	List<TradeAccount> tradeAccounts = pages.getContent();
           	for(int i=0;i<tradeAccounts.size();i++) {
           		TradeAccount entity  = tradeAccounts.get(i);
           		
           		AccountType aty=(AccountType)this.accountTypeService.findById(new Long(entity.getType()));
           		entity.setBackup3(aty.getTypeName());
           		 entity.setSortNum((page - 1) * limit + (i + 1));
           		 
           		 if(entity.getUserId()!=null) {
           			 UserInfo userInfo=(UserInfo)this.userInfoService.findById(entity.getUserId());
           			 entity.setUserInfo(userInfo);
           		 }
           	}
           	
           	datas.put("items", tradeAccounts);
           	datas.put("total", pages.getTotalElements());
           	return ResponseDataUtil.buildSuccess(datas);
       	} catch (Exception e) {
           	e.printStackTrace();
           	return ResponseDataUtil.buildError(e.getMessage());
       	}
   	}


	/**
	 * 新建
	 * @param data
	 * @param request
	 * @return
	 */
	@PostMapping(value = "/add")
	public ResponseData add(@RequestBody JSONObject data,HttpServletRequest request) {
		try { 
			TradeAccount tradeAccount = new TradeAccount();
			
			
			
			tradeAccount.setBackup6(data.getString("backup6"));
			tradeAccount.setType(data.getInteger("type"));
			AccountType atyp=this.accountTypeService.findById(new Long(data.getString("type")));
			
			if (data.containsKey("groupName")) {
				tradeAccount.setGroupName(data.getString("groupName"));
			}else {
				tradeAccount.setGroupName(atyp.getGroupName());
			}
			
			
			//tradeAccount.setLeverId(data.getLong("leverId"));
			//Lever lever=this.leverService.findById(data.getLong("leverId"));
        	tradeAccount.setLeverShow(data.getString("leverShow"));
        	
        	UserInfo ui=null;
        	if (data.containsKey("userAccount")) {
        		
        		
        		
            	tradeAccount.setUserAccount(data.getString("userAccount"));
            	
            	UserInfo query_ui=new UserInfo();
            	
            	if(!data.getString("userAccount").equals("")) {
            	query_ui.setUserName(data.getString("userAccount"));
            	}else {
            		query_ui.setUserName("IDUSI*FIUSHFIUHDS");
            	}
            	
            	Page<UserInfo> pages_ui=this.userInfoService.findAll(0, 1, "id", "asc", query_ui);
            	if(pages_ui.getContent().size()>0) {
            		ui=pages_ui.getContent().get(0);
            		tradeAccount.setUserId(ui.getId());
            	}
            	
        	}else {
        		
        	}
        	tradeAccount.setTradeStatus(1);
        	tradeAccount.setIsDeleted(0);
        	tradeAccount.setIsAvailable(1);
        	
        	
        	//begin
			
		    Long beginID=2000L;
            Long endID=999999999L;
            if(atyp.getBackup1()!=null&&!atyp.getBackup1().equals("")) {//起始账号
            	
            	try {
            		beginID=new Long(atyp.getBackup1().toString());
            	}catch(Exception e) {
            		
            	}
            }
            if(atyp.getBackup2()!=null&&!atyp.getBackup2().equals("")) {//结束账号
            	try {
            		endID=new Long(atyp.getBackup2().toString());
            	}catch(Exception e) {
            		
            	}
            }
            Long taccountID_MR=-1L;
            for(Long cur=beginID;cur<=endID;cur++) {
            	TradeAccount taaa_query=new TradeAccount();
            	taaa_query.setTradeId(cur.toString());
            	Page<TradeAccount> taaa_page=this.tradeAccountService.findAll(0, 1, "id", "asc", taaa_query);
            	
            	if(taaa_page.getContent().size()>0) {
            		
            	}else {
            		 taccountID_MR=cur;
            		 break;
            	}
            }
		
		//end
		if(taccountID_MR!=-1L) {
			tradeAccount.setTradeId(taccountID_MR.toString());//交易账号
			Result re = tradeAccountService.saveOrUpdate(tradeAccount);
			 if(re.getCode().equals(Result.CODEFAIL)){
				 return ResponseDataUtil.buildError("保存失败");
			 }
			 ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
			 //begin crm 接口
			 
			  try {
					 WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
					 client.connect();
					 int mm=0;
					 while (!client.getReadyState().equals(READYSTATE.OPEN)) {
						 Thread.sleep(100);
						 System.out.println("createAccount   "+new Date()+" "+client.getReadyState());
						 mm=mm+1;
						 if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
							 client.reconnect();
						 mm=0;
						 }
					 }
						 HashMap map=new HashMap();
						 map.put("reqtype", "register");
						 map.put("reqid", String.valueOf(new Date().getTime()));
						 map.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
						 if(ui!=null) {
							 map.put("username", new String(ui.getFullname()));
						 }else {
							 if (data.containsKey("fullName")) {
								 map.put("username", data.getString("fullName"));
							 }else {
								 map.put("username", "交易账号"+String.valueOf(tradeAccount.getTradeId()));
							 }
						 }
						 map.put("leverage", new Integer(tradeAccount.getLeverShow()));
						 map.put("groupname", tradeAccount.getGroupName());
						 map.put("password", tradeAccount.getBackup6());
						 map.put("investor", tradeAccount.getBackup6()+"1");
						 map.put("phonepwd", tradeAccount.getBackup6()+"2");
						 JSONObject jsonObj=new JSONObject(map);
						 client.send(jsonObj.toString());
						 
						 Thread.sleep(500);
						 mm=0;
						 while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							 Thread.sleep(100);
							 System.out.println("completAccount   "+new Date()+" "+client.getReadyState());
							 mm=mm+1;
							 if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								 client.reconnect();
							 mm=0;
							 }
						 }
						 HashMap map1=new HashMap();
						 map1.put("reqtype", "updateuserinfo");
						 map1.put("reqid", String.valueOf(new Date().getTime()));
						 map1.put("leverage", new Integer(tradeAccount.getLeverShow()));
						 map1.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
						 if(ui!=null) {
								 map1.put("email", ui.getUserName());
								 map1.put("country", ui.getCountry());
								 map1.put("state", ui.getProvince());
								 map1.put("city",ui.getCity());
								 map1.put("address", ui.getAdress());
								 map1.put("phone", ui.getTel());
						 }else {
							 
							 if (data.containsKey("userName")) {
								 map1.put("email", data.getString("userName"));
							 }else {
								 map1.put("email", "");
							 }
							 map1.put("country","中国");
							 
							 if (data.containsKey("province")) {
								 map1.put("state", data.getString("province"));
							 }else {
								 map1.put("state", "");
							 }
							 
							 if (data.containsKey("city")) {
								 map1.put("city", data.getString("city"));
							 }else {
								 map1.put("city", "");
							 }
							 
							 if (data.containsKey("adress")) {
								 map1.put("address", data.getString("adress"));
							 }else {
								 map1.put("address", "");
							 }
							 
							 if (data.containsKey("tel")) {
								 map1.put("phone", data.getString("tel"));
							 }else {
								 map1.put("phone", "");
							 }
						 }
						 map1.put("enable", 1);//是否启用  1 启用  0 禁用
						 map1.put("enable_change_password", 1);//是否能够修改密码  1 启用  0 禁用
						 map1.put("send_reports", 1);//是否发送报告  1 启用  0 禁用
						 client.send(new JSONObject(map1).toString());
						 
					 }catch(Exception e) {
						 
					 }
				 //操作日志 -begin
				 User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
				 OperLog olog=new OperLog();
				 olog.setBusId(tradeAccount.getId());
				 olog.setBusType(7);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
				 olog.setOperId(loginUser_log.getUserId());
				 olog.setOperName(loginUser_log.getUsername());
				 olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
				 this.operLogService.saveOrUpdate(olog);
				 //操作日志 -end
			 return ResponseDataUtil.buildSuccess(tradeAccount);
		  }else {
			  return ResponseDataUtil.buildError("保存失败");
		  }
			
		} catch (Exception e) { 
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


    /**
	 * 更新
	 * @param data
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
	 		if (data.containsKey("id")) {
	 			Long id = data.getLong("id");
	 			TradeAccount tradeAccount = this.tradeAccountService.findById(id);
				if (tradeAccount != null) {
					
					
					tradeAccount.setType(data.getInteger("type"));
					AccountType atyp=this.accountTypeService.findById(new Long(data.getString("type")));
					
					if (data.containsKey("groupName")) {
						tradeAccount.setGroupName(data.getString("groupName"));
					}else {
						tradeAccount.setGroupName(atyp.getGroupName());
					}
					
					UserInfo ui=null;
					if (data.containsKey("userAccount")) {
		            	tradeAccount.setUserAccount(data.getString("userAccount"));
		            	UserInfo query_ui=new UserInfo();
		            	if(!data.getString("userAccount").equals("")) {
		            	query_ui.setUserName(data.getString("userAccount"));
		            	}else {
		            		query_ui.setUserName("IDUSI*FIUSHFIUHDS");
		            	}
		            	Page<UserInfo> pages_ui=this.userInfoService.findAll(0, 1, "id", "asc", query_ui);
		            	if(pages_ui.getContent().size()>0) {
		            		ui=pages_ui.getContent().get(0);
		            		tradeAccount.setUserId(ui.getId());
		            	}
		            	
		        	}else {
		        		
		        	}
					
					//tradeAccount.setLeverId(data.getLong("leverId"));
					//Lever lever=this.leverService.findById(data.getLong("leverId"));
					tradeAccount.setLeverShow(data.getString("leverShow"));
		        	
		        	
					Result re = tradeAccountService.saveOrUpdate(tradeAccount);
					if(re.getCode().equals(Result.CODEFAIL)){
						 return ResponseDataUtil.buildError("update fail");
					}
					
					try {
						ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
						WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						client.connect();
						int mm=0;
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(100);
							System.out.println("createAccount   "+new Date()+" "+client.getReadyState());
							mm=mm+1;
							if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								client.reconnect();
							mm=0;
							}
						}
						
							HashMap map1=new HashMap();
							map1.put("reqtype", "updateuserinfo");
							map1.put("reqid", String.valueOf(new Date().getTime()));
							map1.put("leverage", new Integer(tradeAccount.getLeverShow()));
							map1.put("groupname", tradeAccount.getGroupName());
							map1.put("login", new Integer(String.valueOf(tradeAccount.getTradeId())));
							map1.put("enable", 1);//是否启用  1 启用  0 禁用
							map1.put("enable_change_password", 1);//是否能够修改密码  1 启用  0 禁用
							map1.put("send_reports", 1);//是否发送报告  1 启用  0 禁用
							client.send(new JSONObject(map1).toString());
						 }catch(Exception e) {
							 
						 }
					
					return ResponseDataUtil.buildSuccess();
				}else{
					return ResponseDataUtil.buildError("data id error");
				}
	 		}else{
				return ResponseDataUtil.buildError("param id error");
	 		}
		} catch (Exception e) {
	 		e.printStackTrace();
	 		return ResponseDataUtil.buildError(e.getMessage());
	 	}
	}
    /**
	 * 删除
	 * @param id
	 * @param request
	 * @return
	 */
	@GetMapping( "/remove")
	public ResponseData remove(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				
				TradeAccount ta=this.tradeAccountService.findById(id);
				ta.setIsAvailable(-1);
				Result result =this.tradeAccountService.saveOrUpdate(ta);
				ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
				//begin crm 接口
				
				 try {
						WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						client.connect();
						int mm=0;
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(100);
							System.out.println("disabledAccount   "+new Date()+" "+client.getReadyState());
							mm=mm+1;
							if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								client.reconnect();
							mm=0;
							}
						}
							HashMap map=new HashMap();
							map.put("reqtype", "updateuserinfo");
							map.put("reqid", String.valueOf(new Date().getTime()));
							map.put("login", new Integer(String.valueOf(ta.getTradeId())));
							map.put("leverage", new Integer(ta.getLeverShow())); 
							map.put("enable", 0);//是否启用  1 启用  0 禁用
							 
						    JSONObject jsonObj=new JSONObject(map);
						    client.send(jsonObj.toString());
						    
						}catch(Exception e) {
							
						}
				if(result.getCode().equals(Result.CODESUCCESS)){
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(5);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(3);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
 		}else{
 			return ResponseDataUtil.buildError("param error");
 		}
	}


    /**
	 * 状态修改
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@GetMapping("/updateIsAvailable")
	public ResponseData updateIsAvailable(@Valid Long id, @Valid Integer isAvailable, HttpServletRequest request) {
		if (id != null && isAvailable != null) { 
			try {
				Result result = tradeAccountService.updateIsAvailableById(id, isAvailable);
				if(result.getCode().equals(Result.CODESUCCESS)){
					return ResponseDataUtil.buildSuccess(); 
				}else{
					return ResponseDataUtil.buildError("update fail"); 
				}
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
		} else {
			return ResponseDataUtil.buildError("param error");		}
	}
	
	@GetMapping( "/audit")
	public ResponseData audit(@Valid Long id, HttpServletRequest request) {
		if(id!=null){
			try {
				TradeAccount ta=this.tradeAccountService.findById(id);
				
				if(ta!=null&&ta.getIsAvailable().intValue()!=1) {
				//String password=new UserInfoController().genRandomNum(8,1)+String.valueOf(new Random().nextInt(99));
				AccountType  atype=(AccountType)this.accountTypeService.findById(new Long(ta.getType()));
				
				ta.setIsAvailable(1);
				ta.setTradeStatus(1);
				String password=ta.getBackup6();
				//begin
				
				    Long beginID=2000L;
	                Long endID=999999999L;
	                if(atype.getBackup1()!=null&&!atype.getBackup1().equals("")) {//起始账号
	                	
	                	try {
	                		beginID=new Long(atype.getBackup1().toString());
	                	}catch(Exception e) {
	                		
	                	}
	                }
	                if(atype.getBackup2()!=null&&!atype.getBackup2().equals("")) {//结束账号
	                	try {
	                		endID=new Long(atype.getBackup2().toString());
	                	}catch(Exception e) {
	                		
	                	}
	                }
	                Long taccountID_MR=-1L;
	                for(Long cur=beginID;cur<=endID;cur++) {
	                	TradeAccount taaa_query=new TradeAccount();
	                	taaa_query.setTradeId(cur.toString());
	                	Page<TradeAccount> taaa_page=this.tradeAccountService.findAll(0, 1, "id", "asc", taaa_query);
	                	
	                	if(taaa_page.getContent().size()>0) {
	                		
	                	}else {
	                		 taccountID_MR=cur;
	                		 break;
	                	}
	                }
				
				//end
				
				
				
	            if(taccountID_MR!=-1L) {
					ta.setTradeId(taccountID_MR.toString());//交易账号
					this.tradeAccountService.saveOrUpdate(ta);
					
					
					UserInfo userInfo=(UserInfo)this.userInfoService.findById(ta.getUserId());
					EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
					CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
					try {
						SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"开户通知","",ta.getTradeId(),password,emailInfo.getBackup2(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,userInfo.getUserName(),userInfo.getFullname(),"");
					}catch(Exception e) {
						System.out.println(e);
					}
					ServerSetting ss=(ServerSetting)this.serverSettingService.findById(1L);
				//begin crm 接口
				
				 try {
						WebSocketClient client= new MyWebsocketClient4Register(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						client.connect();
						int mm=0;
						while (!client.getReadyState().equals(READYSTATE.OPEN)) {
							Thread.sleep(100);
							System.out.println("createAccount   "+new Date()+" "+client.getReadyState());
							mm=mm+1;
							if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
								client.reconnect();
							mm=0;
							}
						}
							HashMap map=new HashMap();
							map.put("reqtype", "register");
							map.put("reqid", String.valueOf(new Date().getTime()));
							map.put("login", new Integer(String.valueOf(ta.getTradeId())));
							map.put("username", new String(userInfo.getFullname()));
							map.put("leverage", new Integer(ta.getLeverShow()));
							map.put("groupname", atype.getGroupName());
							map.put("password", password);
							map.put("investor", password+"1");
							map.put("phonepwd", password+"2");
							JSONObject jsonObj=new JSONObject(map);
							client.send(jsonObj.toString());
							
							Thread.sleep(500);
							mm=0;
							while (!client.getReadyState().equals(READYSTATE.OPEN)) {
								Thread.sleep(100);
								System.out.println("completAccount   "+new Date()+" "+client.getReadyState());
								mm=mm+1;
								if(mm>=300||client.getReadyState().equals(READYSTATE.CLOSED)||client.getReadyState().equals(READYSTATE.NOT_YET_CONNECTED)) {
									client.reconnect();
								mm=0;
								}
							}
							
							HashMap map1=new HashMap();
							map1.put("reqtype", "updateuserinfo");
							map1.put("reqid", String.valueOf(new Date().getTime()));
							map1.put("leverage", new Integer(ta.getLeverShow()));
							map1.put("login", new Integer(String.valueOf(ta.getTradeId())));
							map1.put("email", userInfo.getUserName());
							map1.put("country", userInfo.getCountry());
							map1.put("state", userInfo.getProvince());
							map1.put("city",userInfo.getCity());
							map1.put("address", userInfo.getAdress());
							map1.put("phone", userInfo.getTel());
							map1.put("enable", 1);//是否启用  1 启用  0 禁用
							map1.put("enable_change_password", 1);//是否能够修改密码  1 启用  0 禁用
							map1.put("send_reports", 1);//是否发送报告  1 启用  0 禁用
							client.send(new JSONObject(map1).toString());
							
						}catch(Exception e) {
							
						}
					
				 
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(id);
					olog.setBusType(5);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(4);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					
					return ResponseDataUtil.buildSuccess(); 
					}else {
						ta.setIsAvailable(0);
						 this.tradeAccountService.saveOrUpdate(ta);
						return ResponseDataUtil.buildError("审批失败，超出账号范围");
						
					}
				}else {
					return ResponseDataUtil.buildError("oper error");
				}
				
			}catch(Exception e){
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
 			}
			
 		}else{
 			return ResponseDataUtil.buildError("param id error");
 		}
	}
	
	
	 @PostMapping("/exportTradeAccountExcel")
	    public ResponseData exportTradeAccountExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = {  "交易账号", "账户类型","开户时间","所属用户","CRM账号","交易杠杆","余额","信用","净值","交易手数（30天）","已用预付款","可用预付款"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*556);
	        sheet.setColumnWidth(3, (short)15*256);
	        sheet.setColumnWidth(4, (short)15*356);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setColumnWidth(7, (short)15*256);
	        sheet.setColumnWidth(8, (short)15*256);
	        sheet.setColumnWidth(9, (short)15*256);
	        sheet.setColumnWidth(10, (short)15*256);
	        sheet.setColumnWidth(11, (short)15*256);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
			    TradeAccount query  = new TradeAccount();
		    	String sortBy = "desc";
		    	String sort = "id";
		    	if (request.getParameter("sort")!= null) {
		    		sort = request.getParameter("sort").trim();
		    		if (sort.startsWith("+")) {
		    			sortBy = "asc";
		    		}
		  			sort = sort.replace("+", "").replace("-", "");
		    	}
		    	
		    	if (!StringUtils.isEmpty(request.getParameter("gmtCreateSearchBegin"))) {
	               	query.setGmtCreateSearchBegin(request.getParameter("gmtCreateSearchBegin").trim());
	               	query.setGmtCreateSearchEnd(request.getParameter("gmtCreateSearchEnd").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("tradeId"))) {
	               	query.setTradeId(request.getParameter("tradeId").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("userId"))) {
	               	query.setUserId(Long.parseLong(request.getParameter("userId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("userAccount"))) {
	               	query.setUserAccount(request.getParameter("userAccount").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("type"))) {
	               	query.setType(Integer.parseInt(request.getParameter("type").trim()));
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("groupName"))) {
	               	query.setGroupName(request.getParameter("groupName").trim());
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("leverId"))) {
	               	query.setLeverId(Long.parseLong(request.getParameter("leverId").trim())) ;
	        	}
	        	if (!StringUtils.isEmpty(request.getParameter("tradeStatus"))) {
	               	query.setTradeStatus(Integer.parseInt(request.getParameter("tradeStatus").trim()));
	        	}
	        	query.setIsAvailable(1);
	        	DecimalFormat df = new DecimalFormat(".00");
	        	 SimpleDateFormat format =  new SimpleDateFormat("yyyy-MM-dd HH:mm:ss"); 
	        	 User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	             	
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			
	            			 //1.查出自己及名下所有的代理
	            			 
	            			 User user_query=new User();
	            			 user_query.setSortStr(uu.getSortStr());
	            			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	            			
	            			 
	            			 //2.遍历代理查出所有的crm用户ID
	            			 
	            			 List userList=new ArrayList();
	            			 //2.1 先查询自己名下的crm用户并将crm用户ID放进userList传参
	            			 
	            			 UserInfo ui_self_query=new UserInfo();
	            			 ui_self_query.setParentId(uu.getUserId());
	            			 Page<UserInfo> ui_self=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query);
	            			 for(int m=0;m<ui_self.getContent().size();m++) {
	            				 UserInfo ui_1=(UserInfo)ui_self.getContent().get(m);
	            				 userList.add(ui_1.getId());
	            				 
	            			 }
	            			 if(ui_self.getContent().size()<=0) {
	            				 userList.add(999999L);
	            			 }
	            			 //2.2循环查出自己名下代理的crm用户并将crm用户ID放进userList传参
	            			 
	            			 for(int n=0;n<ul.getContent().size();n++) {
	            				 User user_1=ul.getContent().get(n);
	            				 UserInfo ui_self_query_2=new UserInfo();
	            				 ui_self_query_2.setParentId(user_1.getUserId());
		            			 Page<UserInfo> ui_self2=this.userInfoService.findAll(0, 10000, "id", "asc", ui_self_query_2);
		            			 for(int m1=0;m1<ui_self2.getContent().size();m1++) {
		            				 UserInfo ui_2=(UserInfo)ui_self2.getContent().get(m1);
		            				 userList.add(ui_2.getId());
		            			 }
	            			 }
	            			 query.setUserInfoList(userList);
	            		 }
	            	 }
	             }
	             
	             
	        	Page<TradeAccount> pages = tradeAccountService.findAll(0,100000, sort, sortBy, query);
	        	List<TradeAccount> tradeAccounts = pages.getContent();
	        	for(int i=0;i<tradeAccounts.size();i++) {
	        		TradeAccount entity  = tradeAccounts.get(i);
	        		 
	        		 if(entity.getType()!=null) {
	        		 AccountType aty=(AccountType)this.accountTypeService.findById(new Long(entity.getType()));
	            		entity.setBackup3(aty.getTypeName());
	        		 }
	        		 if(entity.getUserId()!=null) {
	        			 UserInfo userInfo=(UserInfo)this.userInfoService.findById(entity.getUserId());
	        			 entity.setUserInfo(userInfo);
	        		 }else {
	        			 entity.setUserInfo(new UserInfo());
	        		 }
	        		 
	        		 
	        		 
	        		OrderInfo order_query=new OrderInfo();
	     			order_query.setLoginId(String.valueOf(entity.getTradeId()));
	     			order_query.setStatus(2);
	     			List ll=new ArrayList();
	     	    	ll.add(1);
	     	    	ll.add(0);
	     	    	/*ll.add(2);
	     	    	ll.add(3);
	     	    	ll.add(4);
	     	    	ll.add(5);
	     	    	*/
	     	    	order_query.setTypeList(ll);
	     	    	
	     	    	Date date1 = new Date();  
     	    		Date date2 =  new Date();  
     	    		order_query.setQueryBeginTime(new Integer(String.valueOf(date1.getTime()/1000))-(60*60*24*30));
     	    		order_query.setQueryEndTime(new Integer(String.valueOf((date2.getTime()/1000)+86399)));
	     	    	
	     	    	
	     			Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,100000, "id","asc", order_query);
	     			
	     			if(oi_page.getContent().size()<=0) {
	     				entity.setVolume(0d);
	     			}
	     			for(int mmm=0;mmm<oi_page.getContent().size();mmm++) {
	     				OrderInfo oiii=(OrderInfo)oi_page.getContent().get(mmm);
	     				if(entity.getVolume()==null) {
	     					entity.setVolume(new Double(df.format(oiii.getVolume())));
	     				}else {
	     					entity.setVolume(new Double(df.format(entity.getVolume()+oiii.getVolume())));
	     				}
	     			}
	     			
	        	}
			 for(int k=0;k<tradeAccounts.size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   TradeAccount entity  = tradeAccounts.get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(entity.getTradeId()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(entity.getBackup3()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(entity.getGmtCreate().toString()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(entity.getUserInfo().getFullname()));
	               row.createCell(4).setCellValue(new HSSFRichTextString(entity.getUserAccount()));
	               row.createCell(5).setCellValue(new HSSFRichTextString(entity.getLeverShow()));
	               row.createCell(6).setCellValue(new HSSFRichTextString(entity.getBalance1().toString()));
	               row.createCell(7).setCellValue(new HSSFRichTextString(entity.getBalance2().toString()));
	               row.createCell(8).setCellValue(new HSSFRichTextString(entity.getBalance3().toString()));
	               row.createCell(9).setCellValue(new HSSFRichTextString(entity.getVolume().toString()));
	               row.createCell(10).setCellValue(new HSSFRichTextString(entity.getBalance4().toString()));
	               row.createCell(11).setCellValue(new HSSFRichTextString(entity.getBalance5().toString()));
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}
	
	

}

