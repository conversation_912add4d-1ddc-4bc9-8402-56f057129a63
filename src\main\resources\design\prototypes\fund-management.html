<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USK外汇CRM - 资金管理</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
            color: #ffffff;
            min-height: 100vh;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(26, 31, 46, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.2);
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            overflow: hidden;
        }

        .nav-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .nav-title {
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-left: 50px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #007AFF;
            background: rgba(0, 122, 255, 0.1);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 主要内容区域 */
        .main-container {
            padding: 30px;
            max-width: 1400px;
            margin: 0 auto;
        }

        .page-header {
            margin-bottom: 30px;
        }

        .page-title {
            font-size: 28px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 8px;
        }

        .page-subtitle {
            color: rgba(255, 255, 255, 0.6);
            font-size: 16px;
        }

        /* 资金操作选项卡 */
        .fund-tabs {
            display: flex;
            background: rgba(26, 31, 46, 0.8);
            border-radius: 12px;
            padding: 6px;
            margin-bottom: 30px;
            border: 1px solid rgba(0, 122, 255, 0.15);
        }

        .fund-tab {
            flex: 1;
            padding: 15px;
            text-align: center;
            border-radius: 8px;
            cursor: pointer;
            transition: all 0.3s ease;
            font-weight: 500;
            color: rgba(255, 255, 255, 0.6);
        }

        .fund-tab.active {
            background: linear-gradient(135deg, #007AFF, #00FF88);
            color: #ffffff;
            box-shadow: 0 10px 20px rgba(0, 122, 255, 0.3);
        }

        .fund-tab:hover:not(.active) {
            background: rgba(0, 122, 255, 0.1);
            color: rgba(255, 255, 255, 0.8);
        }

        /* 内容区域 */
        .content-grid {
            display: grid;
            grid-template-columns: 1fr 400px;
            gap: 30px;
        }

        .main-content {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 30px;
        }

        .sidebar-content {
            display: flex;
            flex-direction: column;
            gap: 20px;
        }

        /* 表单样式 */
        .form-section {
            margin-bottom: 30px;
        }

        .section-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
            padding-bottom: 10px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .form-grid {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 20px;
        }

        .form-group {
            margin-bottom: 20px;
        }

        .form-group.full-width {
            grid-column: 1 / -1;
        }

        .form-label {
            display: block;
            color: rgba(255, 255, 255, 0.8);
            font-size: 14px;
            font-weight: 500;
            margin-bottom: 8px;
        }

        .form-input, .form-select {
            width: 100%;
            padding: 16px 20px;
            background: rgba(45, 58, 79, 0.6);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 12px;
            color: #ffffff;
            font-size: 16px;
            transition: all 0.3s ease;
            backdrop-filter: blur(10px);
        }

        .form-input:focus, .form-select:focus {
            outline: none;
            border-color: #007AFF;
            background: rgba(45, 58, 79, 0.8);
            box-shadow: 0 0 20px rgba(0, 122, 255, 0.2);
        }

        .form-input::placeholder {
            color: rgba(255, 255, 255, 0.4);
        }

        /* 文件上传区域 */
        .upload-area {
            border: 2px dashed rgba(0, 122, 255, 0.3);
            border-radius: 12px;
            padding: 40px;
            text-align: center;
            background: rgba(0, 122, 255, 0.05);
            transition: all 0.3s ease;
            cursor: pointer;
        }

        .upload-area:hover {
            border-color: rgba(0, 122, 255, 0.6);
            background: rgba(0, 122, 255, 0.1);
        }

        .upload-icon {
            font-size: 48px;
            margin-bottom: 15px;
            color: #007AFF;
        }

        .upload-text {
            color: rgba(255, 255, 255, 0.8);
            font-size: 16px;
            margin-bottom: 8px;
        }

        .upload-hint {
            color: rgba(255, 255, 255, 0.5);
            font-size: 14px;
        }

        /* 银行信息显示 */
        .bank-info {
            background: rgba(0, 122, 255, 0.1);
            border: 1px solid rgba(0, 122, 255, 0.2);
            border-radius: 12px;
            padding: 20px;
            margin-bottom: 20px;
        }

        .bank-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 15px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        .bank-details {
            display: grid;
            grid-template-columns: 1fr 1fr;
            gap: 15px;
        }

        .bank-item {
            display: flex;
            justify-content: space-between;
            padding: 8px 0;
        }

        .bank-label {
            color: rgba(255, 255, 255, 0.6);
            font-size: 14px;
        }

        .bank-value {
            color: #ffffff;
            font-family: 'Courier New', monospace;
            font-weight: 500;
        }

        /* 提交按钮 */
        .submit-button {
            width: 100%;
            padding: 18px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border: none;
            border-radius: 12px;
            color: white;
            font-size: 16px;
            font-weight: 600;
            cursor: pointer;
            transition: all 0.3s ease;
            margin-top: 20px;
        }

        .submit-button:hover {
            transform: translateY(-2px);
            box-shadow: 0 15px 35px rgba(0, 122, 255, 0.4);
        }

        .submit-button:disabled {
            background: rgba(255, 255, 255, 0.2);
            cursor: not-allowed;
            transform: none;
            box-shadow: none;
        }

        /* 侧边栏组件 */
        .sidebar-card {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 25px;
        }

        .card-title {
            font-size: 16px;
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 20px;
            display: flex;
            align-items: center;
            gap: 10px;
        }

        /* 账户选择器 */
        .account-selector {
            margin-bottom: 20px;
        }

        .account-option {
            padding: 15px;
            background: rgba(45, 58, 79, 0.4);
            border: 1px solid rgba(255, 255, 255, 0.1);
            border-radius: 8px;
            margin-bottom: 10px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .account-option:hover, .account-option.selected {
            border-color: #007AFF;
            background: rgba(0, 122, 255, 0.2);
        }

        .account-name {
            font-weight: 600;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .account-balance {
            font-family: 'Courier New', monospace;
            color: #00FF88;
            font-size: 14px;
        }

        /* 费用计算 */
        .fee-calculation {
            background: rgba(0, 0, 0, 0.2);
            border-radius: 8px;
            padding: 15px;
        }

        .fee-item {
            display: flex;
            justify-content: space-between;
            margin-bottom: 10px;
            font-size: 14px;
        }

        .fee-label {
            color: rgba(255, 255, 255, 0.6);
        }

        .fee-value {
            color: #ffffff;
            font-family: 'Courier New', monospace;
        }

        .fee-total {
            border-top: 1px solid rgba(255, 255, 255, 0.1);
            padding-top: 10px;
            margin-top: 10px;
            font-weight: 600;
            font-size: 16px;
        }

        .fee-total .fee-value {
            color: #00FF88;
        }

        /* 最近操作记录 */
        .recent-operations {
            max-height: 300px;
            overflow-y: auto;
        }

        .operation-item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            gap: 15px;
        }

        .operation-icon {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .operation-icon.deposit {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
        }

        .operation-icon.withdraw {
            background: rgba(255, 59, 48, 0.2);
            color: #FF3B30;
        }

        .operation-icon.transfer {
            background: rgba(0, 122, 255, 0.2);
            color: #007AFF;
        }

        .operation-info {
            flex: 1;
        }

        .operation-title {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .operation-time {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.5);
        }

        .operation-amount {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            font-size: 14px;
            color: #00FF88;
        }

        /* 隐藏的内容区域 */
        .tab-content {
            display: none;
        }

        .tab-content.active {
            display: block;
        }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .content-grid {
                grid-template-columns: 1fr;
                gap: 20px;
            }
            
            .sidebar-content {
                flex-direction: row;
                overflow-x: auto;
            }
            
            .sidebar-card {
                min-width: 300px;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
            }
            
            .form-grid {
                grid-template-columns: 1fr;
            }
            
            .fund-tabs {
                flex-direction: column;
            }
            
            .nav-menu {
                display: none;
            }
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-left">
            <div class="nav-logo"><img src="logo.png" alt="USK"></div>
            <div class="nav-title">USK FOREX CRM</div>
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-item">账户总览</a>
                <a href="fund-management.html" class="nav-item active">资金管理</a>
                <a href="trading-records.html" class="nav-item">交易记录</a>
                <a href="profile.html" class="nav-item">个人信息</a>
            </div>
        </div>
        <div class="nav-right">
            <div class="nav-user">
                <div class="user-avatar">张</div>
                <span>张先生</span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <div class="page-header">
            <h1 class="page-title">资金管理</h1>
            <p class="page-subtitle">安全、快速的资金操作服务</p>
        </div>

        <!-- 资金操作选项卡 -->
        <div class="fund-tabs">
            <div class="fund-tab active" data-tab="deposit">
                💰 入金申请
            </div>
            <div class="fund-tab" data-tab="withdraw">
                💳 出金申请
            </div>
            <div class="fund-tab" data-tab="transfer">
                🔄 内部转账
            </div>
        </div>

        <div class="content-grid">
            <!-- 主要内容区域 -->
            <div class="main-content">
                <!-- 入金内容 -->
                <div class="tab-content active" id="deposit-content">
                    <div class="form-section">
                        <h3 class="section-title">📥 入金信息</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">目标账户</label>
                                <select class="form-select" id="deposit-account">
                                    <option value="">选择交易账户</option>
                                    <option value="MT5-********">MT5-******** (标准账户)</option>
                                    <option value="MT5-********">MT5-******** (ECN账户)</option>
                                    <option value="MT5-********">MT5-******** (迷你账户)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">入金金额 (USD)</label>
                                <input type="number" class="form-input" placeholder="请输入入金金额" id="deposit-amount">
                            </div>
                            <div class="form-group">
                                <label class="form-label">汇款银行国家</label>
                                <select class="form-select">
                                    <option value="">选择银行所在国家</option>
                                    <option value="CN">中国</option>
                                    <option value="HK">香港</option>
                                    <option value="SG">新加坡</option>
                                    <option value="US">美国</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">汇款方式</label>
                                <select class="form-select">
                                    <option value="wire">银行电汇</option>
                                    <option value="online">网银转账</option>
                                    <option value="crypto">加密货币</option>
                                </select>
                            </div>
                        </div>
                    </div>

                    <!-- 银行信息 -->
                    <div class="form-section">
                        <h3 class="section-title">🏦 收款银行信息</h3>
                        <div class="bank-info">
                            <div class="bank-title">
                                🏛️ USK FOREX 收款银行
                            </div>
                            <div class="bank-details">
                                <div class="bank-item">
                                    <span class="bank-label">银行名称</span>
                                    <span class="bank-value">汇丰银行 (香港)</span>
                                </div>
                                <div class="bank-item">
                                    <span class="bank-label">账户名称</span>
                                    <span class="bank-value">USK FOREX LIMITED</span>
                                </div>
                                <div class="bank-item">
                                    <span class="bank-label">账户号码</span>
                                    <span class="bank-value">004-567-890123-456</span>
                                </div>
                                <div class="bank-item">
                                    <span class="bank-label">SWIFT代码</span>
                                    <span class="bank-value">HSBCHKHHHKH</span>
                                </div>
                                <div class="bank-item">
                                    <span class="bank-label">银行地址</span>
                                    <span class="bank-value">香港中环皇后大道中1号</span>
                                </div>
                                <div class="bank-item">
                                    <span class="bank-label">备注信息</span>
                                    <span class="bank-value">请务必备注您的MT5账号</span>
                                </div>
                            </div>
                        </div>
                    </div>

                    <!-- 上传凭证 -->
                    <div class="form-section">
                        <h3 class="section-title">📎 上传转账凭证</h3>
                        <div class="upload-area" onclick="document.getElementById('file-input').click()">
                            <div class="upload-icon">📤</div>
                            <div class="upload-text">点击上传转账凭证</div>
                            <div class="upload-hint">支持 JPG、PNG、PDF 格式，最大 10MB</div>
                            <input type="file" id="file-input" style="display: none;" accept=".jpg,.jpeg,.png,.pdf">
                        </div>
                    </div>

                    <button class="submit-button">
                        提交入金申请
                    </button>
                </div>

                <!-- 出金内容 -->
                <div class="tab-content" id="withdraw-content">
                    <div class="form-section">
                        <h3 class="section-title">📤 出金信息</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">源账户</label>
                                <select class="form-select" id="withdraw-account">
                                    <option value="">选择交易账户</option>
                                    <option value="MT5-********">MT5-******** (可用: $156,847.32)</option>
                                    <option value="MT5-********">MT5-******** (可用: $89,325.15)</option>
                                    <option value="MT5-********">MT5-******** (可用: $45,678.90)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">出金金额 (USD)</label>
                                <input type="number" class="form-input" placeholder="请输入出金金额" id="withdraw-amount">
                            </div>
                            <div class="form-group">
                                <label class="form-label">收款银行</label>
                                <select class="form-select">
                                    <option value="">选择收款银行账户</option>
                                    <option value="icbc">中国工商银行 (****1234)</option>
                                    <option value="abc">中国农业银行 (****5678)</option>
                                    <option value="boc">中国银行 (****9012)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">出金原因</label>
                                <select class="form-select">
                                    <option value="profit">提取利润</option>
                                    <option value="capital">提取本金</option>
                                    <option value="emergency">紧急资金需求</option>
                                    <option value="other">其他原因</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">备注说明</label>
                                <input type="text" class="form-input" placeholder="请输入备注信息（可选）">
                            </div>
                        </div>
                    </div>

                    <button class="submit-button">
                        提交出金申请
                    </button>
                </div>

                <!-- 转账内容 -->
                <div class="tab-content" id="transfer-content">
                    <div class="form-section">
                        <h3 class="section-title">🔄 账户间转账</h3>
                        <div class="form-grid">
                            <div class="form-group">
                                <label class="form-label">源账户</label>
                                <select class="form-select" id="transfer-from">
                                    <option value="">选择源账户</option>
                                    <option value="MT5-********">MT5-******** (可用: $156,847.32)</option>
                                    <option value="MT5-********">MT5-******** (可用: $89,325.15)</option>
                                    <option value="MT5-********">MT5-******** (可用: $45,678.90)</option>
                                </select>
                            </div>
                            <div class="form-group">
                                <label class="form-label">目标账户</label>
                                <select class="form-select" id="transfer-to">
                                    <option value="">选择目标账户</option>
                                    <option value="MT5-********">MT5-******** (标准账户)</option>
                                    <option value="MT5-********">MT5-******** (ECN账户)</option>
                                    <option value="MT5-********">MT5-******** (迷你账户)</option>
                                </select>
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">转账金额 (USD)</label>
                                <input type="number" class="form-input" placeholder="请输入转账金额" id="transfer-amount">
                            </div>
                            <div class="form-group full-width">
                                <label class="form-label">转账备注</label>
                                <input type="text" class="form-input" placeholder="请输入转账备注（可选）">
                            </div>
                        </div>
                    </div>

                    <button class="submit-button">
                        确认转账
                    </button>
                </div>
            </div>

            <!-- 侧边栏内容 -->
            <div class="sidebar-content">
                <!-- 账户余额 -->
                <div class="sidebar-card">
                    <h3 class="card-title">
                        💳 账户余额
                    </h3>
                    <div class="account-selector">
                        <div class="account-option selected">
                            <div class="account-name">MT5-********</div>
                            <div class="account-balance">可用余额: $156,847.32</div>
                        </div>
                        <div class="account-option">
                            <div class="account-name">MT5-********</div>
                            <div class="account-balance">可用余额: $89,325.15</div>
                        </div>
                        <div class="account-option">
                            <div class="account-name">MT5-********</div>
                            <div class="account-balance">可用余额: $45,678.90</div>
                        </div>
                    </div>
                </div>

             

                <!-- 最近操作 -->
                <div class="sidebar-card">
                    <h3 class="card-title">
                        📊 最近操作
                    </h3>
                    <div class="recent-operations">
                        <div class="operation-item">
                            <div class="operation-icon deposit">↓</div>
                            <div class="operation-info">
                                <div class="operation-title">入金成功</div>
                                <div class="operation-time">2小时前</div>
                            </div>
                            <div class="operation-amount">+$50,000</div>
                        </div>
                       
                        <div class="operation-item">
                            <div class="operation-icon withdraw">↑</div>
                            <div class="operation-info">
                                <div class="operation-title">出金成功</div>
                                <div class="operation-time">1天前</div>
                            </div>
                            <div class="operation-amount">-$15,000</div>
                        </div>
                        <div class="operation-item">
                            <div class="operation-icon deposit">↓</div>
                            <div class="operation-info">
                                <div class="operation-title">入金成功</div>
                                <div class="operation-time">2天前</div>
                            </div>
                            <div class="operation-amount">+$30,000</div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <script>
        // 选项卡切换功能
        document.querySelectorAll('.fund-tab').forEach(tab => {
            tab.addEventListener('click', function() {
                const tabName = this.getAttribute('data-tab');
                
                // 切换选项卡活动状态
                document.querySelectorAll('.fund-tab').forEach(t => t.classList.remove('active'));
                this.classList.add('active');
                
                // 切换内容显示
                document.querySelectorAll('.tab-content').forEach(content => {
                    content.classList.remove('active');
                });
                document.getElementById(tabName + '-content').classList.add('active');
            });
        });

        // 账户选择器
        document.querySelectorAll('.account-option').forEach(option => {
            option.addEventListener('click', function() {
                document.querySelectorAll('.account-option').forEach(o => o.classList.remove('selected'));
                this.classList.add('selected');
            });
        });

        // 费用计算
        function updateFeeCalculation() {
            const depositAmount = document.getElementById('deposit-amount');
            const withdrawAmount = document.getElementById('withdraw-amount');
            const transferAmount = document.getElementById('transfer-amount');
            
            let amount = 0;
            if (depositAmount && depositAmount.value) {
                amount = parseFloat(depositAmount.value) || 0;
            } else if (withdrawAmount && withdrawAmount.value) {
                amount = parseFloat(withdrawAmount.value) || 0;
            } else if (transferAmount && transferAmount.value) {
                amount = parseFloat(transferAmount.value) || 0;
            }
            
            const fee = amount * 0.002; // 0.2% 手续费
            const exchangeFee = amount * 0.001; // 0.1% 汇率费用
            const total = amount - fee - exchangeFee;
            
            document.querySelectorAll('.fee-item .fee-value')[0].textContent = '$' + amount.toFixed(2);
            document.querySelectorAll('.fee-item .fee-value')[1].textContent = '$' + fee.toFixed(2);
            document.querySelectorAll('.fee-item .fee-value')[2].textContent = '$' + exchangeFee.toFixed(2);
            document.querySelectorAll('.fee-total .fee-value')[0].textContent = '$' + total.toFixed(2);
        }

        // 绑定金额输入事件
        document.addEventListener('input', function(e) {
            if (e.target.type === 'number') {
                updateFeeCalculation();
            }
        });

        // 文件上传处理
        document.getElementById('file-input').addEventListener('change', function(e) {
            const file = e.target.files[0];
            if (file) {
                const uploadArea = document.querySelector('.upload-area');
                uploadArea.innerHTML = `
                    <div class="upload-icon">✅</div>
                    <div class="upload-text">文件上传成功</div>
                    <div class="upload-hint">${file.name} (${(file.size / 1024 / 1024).toFixed(2)} MB)</div>
                `;
            }
        });

        // 表单验证
        document.querySelectorAll('.submit-button').forEach(button => {
            button.addEventListener('click', function(e) {
                e.preventDefault();
                
                // 简单的表单验证逻辑
                const activeContent = document.querySelector('.tab-content.active');
                const requiredFields = activeContent.querySelectorAll('input[required], select[required]');
                let isValid = true;
                
                requiredFields.forEach(field => {
                    if (!field.value) {
                        isValid = false;
                        field.style.borderColor = '#FF3B30';
                    } else {
                        field.style.borderColor = '';
                    }
                });
                
                if (isValid) {
                    // 模拟提交成功
                    this.textContent = '提交中...';
                    this.disabled = true;
                    
                    setTimeout(() => {
                        alert('申请提交成功！我们将在1-2个工作日内处理您的申请。');
                        this.textContent = this.textContent.replace('提交中...', '提交成功');
                        this.style.background = 'rgba(0, 255, 136, 0.8)';
                    }, 2000);
                } else {
                    alert('请填写所有必填字段');
                }
            });
        });
    </script>
</body>
</html> 