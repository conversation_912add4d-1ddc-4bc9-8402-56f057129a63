<!DOCTYPE html>
<html th:lang="${userInfo?.backup1 ?: 'zh-CN'}">
<head>
  <meta charset="utf-8"/>
  <meta name="viewport" content="width=device-width, initial-scale=1, viewport-fit=cover"/>
  <meta http-equiv="X-UA-Compatible" content="ie=edge"/>
  <meta http-equiv="Content-Security-Policy" content="upgrade-insecure-requests">
  <title>[[${CRM_TITLE}]]</title>
  <!-- CSS files -->
  <link href="../dist/css/tabler.min.css?**********" rel="stylesheet"/>
  <link href="../dist/css/tabler-flags.min.css?**********" rel="stylesheet"/>
  <link href="../dist/css/tabler-payments.min.css?**********" rel="stylesheet"/>
  <link href="../dist/css/tabler-vendors.min.css?**********" rel="stylesheet"/>
  <link href="../dist/css/demo.min.css?**********" rel="stylesheet"/>
  <script src="js/jquery.min.js"></script>
  <script src="js/layer/layer.js"></script>
  <script src="../dist/js/tabler.min.js?**********" defer></script>
  <link rel="shortcut icon" th:href="${CRM_LOGO2}">
  <script async th:src="@{'https://www.googletagmanager.com/gtag/js?id='+${JS_ID1}}" th:if="${HEADER_SCRIPT_STATUS}==1"></script>
  <script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1' ">
    window.dataLayer = window.dataLayer || [];
    function gtag(){dataLayer.push(arguments);} 
    gtag('js', new Date());
    gtag('config', [[${JS_ID1}]]);
  </script>
  <script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">
  !function(f,b,e,v,n,t,s){if(f.fbq)return;n=f.fbq=function(){n.callMethod?
  n.callMethod.apply(n,arguments):n.queue.push(arguments)};if(!f._fbq)f._fbq=n;n.push=n;
  n.loaded=!0;n.version='2.0';n.queue=[];t=b.createElement(e);t.async=!0;
  t.src=v;s=b.getElementsByTagName(e)[0];s.parentNode.insertBefore(t,s)}(window, document,'script',
  'https://connect.facebook.net/en_US/fbevents.js');fbq('init', [[${JS_ID2}]]);fbq('track', 'PageView');
  </script>
  <noscript>
    <img th:if="${HEADER_SCRIPT_STATUS} eq '1'" height="1" width="1" style="display:none"
         th:src="@{'https://www.facebook.com/tr?id='+${JS_ID2}+'&ev=PageView&noscript=1'}"/>
  </noscript>
  <!-- Google Tag Manager -->
  <script th:inline="javascript" th:if="${HEADER_SCRIPT_STATUS} eq '1'">(function(w,d,s,l,i){w[l]=w[l]||[];w[l].push({'gtm.start':
  new Date().getTime(),event:'gtm.js'});var f=d.getElementsByTagName(s)[0],j=d.createElement(s),
  dl=l!='dataLayer'?'&l='+l:'';j.async=true;j.src='https://www.googletagmanager.com/gtm.js?id='+i+dl;
  f.parentNode.insertBefore(j,f);})(window,document,'script','dataLayer',[[${JS_ID3}]]);</script>
  <!-- End Google Tag Manager -->
  <style>
    @import url('https://rsms.me/inter/inter.css');
    * { margin: 0; padding: 0; box-sizing: border-box; }
    body {
      font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
      background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
      color: #ffffff;
      min-height: 100vh;
      overflow-x: hidden;
    }
    /* 顶部导航栏（统一规范） */
    .navbar { background: rgba(26,31,46,0.95); backdrop-filter: blur(20px); border-bottom: 1px solid rgba(0,122,255,0.2); padding: 0 20px; min-height: 70px; display: flex; align-items: center; justify-content: space-between; position: sticky; top: 0; z-index: 1000; flex-wrap: nowrap; overflow: visible; }
    .nav-left { display: flex; align-items: center; flex: 1; min-width: 0; }
    .nav-logo { width: 40px; height: 40px; border-radius: 8px; display: flex; align-items: center; justify-content: center; margin-right: 15px; overflow: hidden; }
    .nav-logo img { width: 100%; height: 100%; object-fit: contain; }
    .nav-title { font-size: 18px; font-weight: 500; letter-spacing: 1px; color: #ffffff; white-space: nowrap; flex-shrink: 0; }
    .nav-menu { display: flex; align-items: center; gap: 15px; margin-left: 30px; flex: 1; min-width: 0; overflow: visible; padding: 5px 0; }
    .nav-item { color: #ffffff; text-decoration: none; padding: 8px 12px; border-radius: 8px; transition: all .3s ease; font-size: 13px; white-space: nowrap; flex-shrink: 0; }
    .nav-item:hover, .nav-item.active { color: #007AFF; background: rgba(0,122,255,0.1); }
    .nav-right { display: flex; align-items: center; gap: 15px; flex-shrink: 0; overflow: visible !important; position: relative; }
    .nav-user { display: flex; align-items: center; gap: 8px; padding: 6px 12px; background: rgba(0,122,255,0.1); border-radius: 20px; cursor: pointer; flex-shrink: 0; }
    .nav-user span { white-space: nowrap; font-size: 13px; color: #ffffff; }
    /* 桌面/移动端用户显示切换（与入金页一致） */
    .mobile-user { display: none; }
    .desktop-user { display: inline; }
    .language-dropdown .dropdown-menu { z-index: 10000 !important; position: absolute !important; top: 100% !important; right: 0 !important; left: auto !important; background: rgba(26,31,46,0.98) !important; backdrop-filter: blur(20px) !important; border: 1px solid rgba(0,122,255,0.3) !important; border-radius: 8px !important; min-width: 180px !important; margin-top: 8px !important; box-shadow: 0 10px 40px rgba(0,0,0,0.4) !important; transform: none !important; }
    .language-dropdown .dropdown-item { color: rgba(255,255,255,0.8) !important; padding: 10px 16px !important; font-size: 14px !important; border-bottom: 1px solid rgba(255,255,255,0.1) !important; transition: all .2s ease !important; }
    .language-dropdown .dropdown-item:hover { background: rgba(0,122,255,0.15) !important; color: #fff !important; }
    .language-dropdown .dropdown-item:last-child { border-bottom: none !important; }
    /* 隐藏语言下拉默认箭头 */
    .language-dropdown .dropdown-toggle::after { display: none !important; }
    /* 操作按钮样式（与 deposit/main 对齐） */
    .action-icon-btn {
        padding: 10px;
        display: flex;
        align-items: center;
        justify-content: center;
        border-radius: 10px;
        transition: all 0.3s ease;
        text-decoration: none;
        border: none;
        backdrop-filter: blur(10px);
        position: relative;
        overflow: hidden;
    }

    .action-icon-btn.modify-pwd {
        background: rgba(0, 122, 255, 0.2);
    }

    .action-icon-btn.modify-pwd:hover {
        background: rgba(0, 122, 255, 0.35);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(0, 122, 255, 0.4);
    }

    .action-icon-btn.logout {
        background: rgba(255, 59, 48, 0.2);
    }

    .action-icon-btn.logout:hover {
        background: rgba(255, 59, 48, 0.35);
        transform: translateY(-2px);
        box-shadow: 0 8px 25px rgba(255, 59, 48, 0.4);
    }

    .action-icon-btn svg {
        color: rgba(255, 255, 255, 0.9);
        transition: all 0.3s ease;
    }

    .action-icon-btn:hover svg {
        color: rgba(255, 255, 255, 1);
        transform: scale(1.1);
    }

    .action-icon-btn.modify-pwd svg {
        color: #007AFF;
    }

    .action-icon-btn.logout svg {
        color: #FF3B30;
    }
    /* 主要内容区域和选项卡 */
    .main-container { padding: 30px; max-width: 1400px; margin: 0 auto; }
    .page-header { margin-bottom: 30px; }
    .page-title { font-size: 28px; font-weight: 600; color: #ffffff; margin-bottom: 8px; }
    .page-subtitle { color: rgba(255,255,255,0.6); font-size: 16px; }
    .fund-tabs { display: flex; background: rgba(26,31,46,0.8); border-radius: 12px; padding: 6px; margin-bottom: 30px; border: 1px solid rgba(0,122,255,0.15); }
    .fund-tab { flex: 1; padding: 15px; text-align: center; border-radius: 8px; cursor: pointer; transition: all .3s ease; font-weight: 500; color: rgba(255,255,255,0.6); text-decoration: none; }
    .fund-tab.active { background: linear-gradient(135deg, #007AFF, #00FF88); color: #ffffff; box-shadow: 0 10px 20px rgba(0,122,255,0.3); }
    .fund-tab:hover:not(.active) { background: rgba(0,122,255,0.1); color: rgba(255,255,255,0.8); }
    .content-grid { display: grid; grid-template-columns: 1fr 400px; gap: 30px; }
    .main-content { background: rgba(26,31,46,0.8); backdrop-filter: blur(20px); border: 1px solid rgba(0,122,255,0.15); border-radius: 16px; padding: 30px; }
    .sidebar-content { display: flex; flex-direction: column; gap: 20px; }
    .form-section { margin-bottom: 30px; }
    .section-title { font-size: 18px; font-weight: 600; color: #ffffff; margin-bottom: 20px; padding-bottom: 10px; border-bottom: 1px solid rgba(255,255,255,0.1); }
    .form-grid { display: grid; grid-template-columns: 1fr 1fr; gap: 20px; }
    .form-group { margin-bottom: 20px; }
    .form-group.full-width { grid-column: 1 / -1; }
    .form-label { display: block; color: rgba(255,255,255,0.8); font-size: 14px; font-weight: 500; margin-bottom: 8px; }
    .form-control, .form-select { width: 100%; padding: 16px 20px; background: rgba(45,58,79,0.6); border: 1px solid rgba(255,255,255,0.1); border-radius: 12px; color: #ffffff; font-size: 16px; transition: all .3s ease; backdrop-filter: blur(10px); }
    .form-control:focus, .form-select:focus { outline: none; border-color: #007AFF; background: rgba(45,58,79,0.8); box-shadow: 0 0 20px rgba(0,122,255,0.2); }
    .submit-button, .btn-primary { width: 100%; padding: 18px; background: linear-gradient(135deg, #007AFF, #00FF88); border: none; border-radius: 12px; color: #fff; font-size: 16px; font-weight: 600; cursor: pointer; transition: all .3s ease; margin-top: 20px; }
    .submit-button:hover, .btn-primary:hover { transform: translateY(-2px); box-shadow: 0 15px 35px rgba(0,122,255,0.4); }
    .sidebar-card { background: rgba(26,31,46,0.8); backdrop-filter: blur(20px); border: 1px solid rgba(0,122,255,0.15); border-radius: 16px; padding: 25px; }
    /* 账户余额卡片与最近记录样式（与入金页对齐） */
    .account-balance-card { background: rgba(26,31,46,0.9); backdrop-filter: blur(20px); border: 2px solid #007AFF; border-radius: 12px; padding: 20px; margin-bottom: 15px; transition: all 0.3s ease; }
    .account-balance-card:hover { border-color: #0056CC; transform: translateY(-2px); box-shadow: 0 8px 25px rgba(0, 122, 255, 0.3); }
    .account-balance-card.selected { border-color: #007AFF; background: rgba(0,122,255,0.1); }
    .account-id { color: #ffffff; font-size: 18px; font-weight: 800; margin-bottom: 8px; font-family: 'Courier New', monospace; }
    .account-balance-label { color: rgba(255,255,255,0.7); font-size: 14px; margin-bottom: 5px; }
    .account-balance-amount { color: #00FF88; font-size: 16px; font-weight: 600; font-family: 'Courier New', monospace; }
    .balance-card-icon { float: left; margin-right: 12px; font-size: 20px; color: #007AFF; }
    .recent-deposit-item { background: rgba(45,58,79,0.4); border: 1px solid rgba(255,255,255,0.1); border-radius: 8px; padding: 15px; margin-bottom: 12px; transition: all 0.3s ease; }
    .recent-deposit-item:hover { background: rgba(45,58,79,0.6); border-color: rgba(0,122,255,0.3); }
    .deposit-record-header { display: flex; justify-content: space-between; align-items: center; margin-bottom: 8px; }
    .deposit-amount { color: #00FF88; font-weight: 600; font-size: 16px; font-family: 'Courier New', monospace; }
    /* 出金金额显示为红色，仅作用于本页最近出金列表 */
    .withdraw-list .deposit-amount { color: #FF3B30; }
    .deposit-status { padding: 4px 8px; border-radius: 4px; font-size: 12px; font-weight: 500; }
    .deposit-status.pending { background: rgba(255, 193, 7, 0.2); color: #FFC107; }
    .deposit-status.success { background: rgba(0, 255, 136, 0.2); color: #00FF88; }
    .deposit-status.rejected { background: rgba(255, 59, 48, 0.2); color: #FF3B30; }
    .deposit-date { color: rgba(255,255,255,0.6); font-size: 12px; }
    .deposit-account { color: rgba(255,255,255,0.8); font-size: 13px; font-family: 'Courier New', monospace; }
    /* 页脚（统一规范） */
    .footer { background: rgba(26,31,46,0.8); backdrop-filter: blur(20px); border-top: 1px solid rgba(0,122,255,0.15); color: rgba(255,255,255,0.8); }
    @media (max-width: 1200px) { .content-grid { grid-template-columns: 1fr; gap: 20px; } .sidebar-content { flex-direction: row; overflow-x: auto; } .sidebar-card { min-width: 300px; } }
    /* 移动端导航与菜单（与入金页一致） */
    .mobile-menu-btn { display: none; }
    .mobile-menu { display: none !important; }
    @media (max-width: 768px) { 
      .main-container { padding: 20px; } 
      .form-grid { grid-template-columns: 1fr; } 
      .nav-menu { display: none; }
      .navbar { min-height: 60px; padding: 10px 16px; }
      .nav-left { display: flex; align-items: center; gap: 12px; }
      .nav-logo { width: 35px; height: 35px; margin-right: 12px; }
      .nav-title { display: none; }
      .nav-user { background: transparent; padding: 0; border-radius: 0; }
      .nav-user span { max-width: 140px; font-size: 14px; color: #ffffff; overflow: hidden; text-overflow: ellipsis; white-space: nowrap; }
      .desktop-user { display: none; }
      .mobile-user { display: inline; }
      .nav-right { gap: 8px; align-items: center; }
      .language-dropdown .nav-item { padding: 4px 8px !important; font-size: 12px; }
      .language-dropdown .nav-item span { font-size: 11px !important; }
      .language-dropdown .nav-item img { width: 12px; height: 12px; }
      .mobile-menu-btn { display: flex !important; align-items: center; justify-content: center; width: 40px; height: 40px; background: rgba(0,122,255,0.1); border: 1px solid rgba(0,122,255,0.2); border-radius: 8px; color: #ffffff; cursor: pointer; transition: all .3s ease; }
      .mobile-menu-btn:hover { background: rgba(0,122,255,0.2); border-color: #007AFF; }
      .mobile-menu { position: fixed !important; top: 60px; right: 15px; background: rgba(26,31,46,0.98); backdrop-filter: blur(20px); border: 1px solid rgba(0,122,255,0.3); border-radius: 12px; min-width: 200px; box-shadow: 0 10px 40px rgba(0,0,0,0.4); z-index: 10000; display: none !important; flex-direction: column; padding: 10px 0; }
      .mobile-menu.show { display: flex !important; }
      .mobile-menu a { color: rgba(255,255,255,0.8); text-decoration: none; padding: 12px 20px; font-size: 14px; border-bottom: 1px solid rgba(255,255,255,0.1); transition: all 0.2s ease; display: flex; align-items: center; gap: 10px; }
      .mobile-menu a:hover { background: rgba(0,122,255,0.15); color: rgba(255,255,255,1); }
      .mobile-menu a:last-child { border-bottom: none; }
    }
  </style>
</head>
<body>
  <script src="./dist/js/demo-theme.min.js?**********"></script>
  <!-- 顶部导航栏（统一规范） -->
  <nav class="navbar">
    <div class="nav-left">
      <div class="nav-logo"><img th:src="${CRM_LOGO1}" alt="USK"></div>
      <div class="nav-title" style="color:#ffffff;">[[${CRM_TITLE}]]</div>
      <div class="nav-menu">
        <a href="main" class="nav-item" th:text="#{menu.home}">Home</a>
        <a href="deposit" class="nav-item active" th:text="#{menu.fundtransfer}">Fund Transfer</a>
        <a href="fundList" class="nav-item" th:text="#{menu.fundrecords}">Fund Records</a>
        <a href="orderList" class="nav-item" th:text="#{menu.traderecords}">Trade Records</a>
        <a th:if="${WEB_TRADER_STATUS} eq '1'" th:href="${WEB_TRADER_URL}" target="_blank" class="nav-item" th:text="#{menu.webtrader}">WebTrader</a>
        <a href="profile" class="nav-item" th:text="#{menu.profile}">Profile</a>
        <a th:if="${SUPPORT_STATUS} eq '1'" th:href="${SupportCenter}" target="_blank" class="nav-item" th:text="#{menu.support}">Support</a>
      </div>
    </div>
    <div class="nav-right">
      <div class="nav-user"><span class="desktop-user">[[${userInfo.userName}]]</span><span class="mobile-user">[[${userInfo.userName}]]</span></div>
      <div class="nav-item dropdown language-dropdown">
        <a href="#" class="nav-item dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false" th:aria-label="#{main.language}" style="padding:6px 12px; display:flex; align-items:center; gap:6px; flex-shrink:0;">
          <img src="../img/Langauge.png" width="14" height="14"/>
          <span style="font-size:12px; white-space:nowrap;" th:text="#{main.language}">语言</span>
        </a>
        <ul class="dropdown-menu dropdown-menu-end">
          <li><a href="javascript:updateaLang('zh-cn');" class="dropdown-item">简体中文</a></li>
          <li><a href="javascript:updateaLang('zh-tw');" class="dropdown-item">繁體中文</a></li>
          <li><a href="javascript:updateaLang('en');" class="dropdown-item">English</a></li>
          <li><a href="javascript:updateaLang('th');" class="dropdown-item">ภาษาไทย</a></li>
          <li><a href="javascript:updateaLang('ms');" class="dropdown-item">Bahasa Malay</a></li>
          <li><a href="javascript:updateaLang('id');" class="dropdown-item">Bahasa Indonesia</a></li>
          <li><a href="javascript:updateaLang('vi');" class="dropdown-item">Tiếng Việt</a></li>
          <li><a href="javascript:updateaLang('ja');" class="dropdown-item">日本語</a></li>
        </ul>
      </div>
      <!-- 移动端菜单按钮 -->
      <div class="mobile-menu-btn" onclick="toggleMobileMenu()">
        <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
          <line x1="3" y1="6" x2="21" y2="6"/>
          <line x1="3" y1="12" x2="21" y2="12"/>
          <line x1="3" y1="18" x2="21" y2="18"/>
        </svg>
      </div>
      <!-- 桌面端显示 修改密码/退出 按钮 -->
      <div th:if="${isMobile==0}" style="display:flex; gap:12px;">
        <a href="modifyPwd" class="action-icon-btn modify-pwd" th:title="#{main.modifypassword}">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <rect x="3" y="11" width="18" height="11" rx="2" ry="2"/>
            <circle cx="12" cy="16" r="1"/>
            <path d="M7 11V7a5 5 0 0 1 10 0v4"/>
          </svg>
        </a>
        <a href="logout" class="action-icon-btn logout" th:title="#{main.logout}">
          <svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" viewBox="0 0 24 24" fill="none" stroke="currentColor" stroke-width="2" stroke-linecap="round" stroke-linejoin="round">
            <path d="M9 21H5a2 2 0 0 1-2-2V5a2 2 0 0 1 2-2h4"/>
            <polyline points="16,17 21,12 16,7"/>
            <line x1="21" y1="12" x2="9" y2="12"/>
          </svg>
        </a>
      </div>
    </div>
  </nav>
  <!-- 移动端下拉菜单（与入金页一致） -->
  <div id="mobileMenu" class="mobile-menu">
    <a href="main"><span th:text="#{menu.home}">首页</span></a>
    <a href="deposit"><span th:text="#{menu.fundtransfer}">资金转账</span></a>
    <a href="fundList"><span th:text="#{menu.fundrecords}">资金记录</span></a>
    <a href="orderList"><span th:text="#{menu.traderecords}">交易记录</span></a>
    <a th:if="${WEB_TRADER_STATUS} eq '1'" th:href="${WEB_TRADER_URL}" target="_blank"><span th:text="#{menu.webtrader}">网页交易</span></a>
    <a href="profile"><span th:text="#{menu.profile}">个人资料</span></a>
    <a th:if="${SUPPORT_STATUS} eq '1'" th:href="${SupportCenter}" target="_blank"><span th:text="#{menu.support}">客户支持</span></a>
    <a href="modifyPwd"><span th:text="#{main.modifypassword}">修改密码</span></a>
    <a href="logout"><span th:text="#{main.logout}">退出登录</span></a>
  </div>

  <!-- 主要内容区域：资金管理布局（出金激活） -->
  <div class="main-container">
    <div class="page-header">
      <h1 class="page-title" th:text="#{withdraw.title}">资金管理</h1>
      <p class="page-subtitle" th:text="#{deposit.pagesubtitle}">安全、快速的资金操作服务</p>
    </div>

    <div class="fund-tabs">
      <a href="deposit" class="fund-tab" data-tab="deposit">💰 <span th:text="#{deposit.deposit}">入金申请</span></a>
      <div class="fund-tab active" data-tab="withdraw">💳 <span th:text="#{gobal.withdraw}">出金申请</span></div>
      <a href="transfer" class="fund-tab" data-tab="transfer">🔄 <span th:text="#{gobal.internaltransfer}">内部转账</span></a>
    </div>

    <div class="content-grid">
      <!-- 出金内容 -->
      <div class="main-content">
        <div class="tab-content active" id="withdraw-content">
          <form id="logonForm">
            <div class="form-section">
              <h3 class="section-title">📤 <span th:text="#{withdraw.withdraw}">出金信息</span></h3>
              <div class="form-grid">
                <div class="form-group">
                  <label class="form-label" th:text="#{withdraw.accountnumber}">交易账户</label>
                  <select name="trade_id" id="trade_id" class="form-control" onChange="selectTradeID();">
                    <option th:each="column,columnStat:${tradeList}" th:value="${column.id}">[[${column.tradeId}]]</option>
                  </select>
                  <small class="form-text text-muted ml-3" id="dqye"></small>
                </div>
                <div class="form-group">
                  <label class="form-label required" th:text="#{withdraw.withdrawamount}">出金金额</label>
                  <input type="text" class="form-control" id="withdrawalAmount" name="withdrawalAmount" th:placeholder="#{deposit.enteramount}">
                  <small class="form-text text-muted ml-3" th:text="#{withdraw.prompt5}">保证金不足可能导致持仓被强平</small>
                </div>
                <div class="form-group">
                  <label class="form-label required" th:text="#{withdraw.withdrawcurrency}">出金币种</label>
                  <select class="form-control" name="withdraw_currency" id="withdraw_currency" onChange="selectCurrency();">
                    <option value="USD">USD</option>
                    <option value="THB">THB</option>
                    <option value="MYR">MYR</option>
                    <option value="IDR">IDR</option>
                    <option value="VND">VND</option>
                    <option value="USDT TRC20">USDT TRC20</option>
                    <option value="USDT ERC20">USDT ERC20</option>
                    <option value="BTC">BTC</option>
                  </select>
                </div>
                <div class="form-group full-width" id="withdraw5" style="display:none;">
                  <label class="form-label" th:text="#{common.digitalcurrencyaddress}">数字货币地址</label>
                  <input type="text" class="form-control" id="bank_address2" name="bank_address2" th:placeholder="#{deposit.enterdigitaladdress}">
                </div>
                <div class="form-group" id="withdraw1">
                  <label class="form-label required" th:text="#{withdraw.bankcoutry}">银行国家</label>
                  <select class="form-control" name="tel" id="tel" onChange="selectBankCountry();">
                    <option th:each="column,columnStat:${countries}" th:selected="${column.countryCodeTwo eq mybank.tel}" th:value="${column.countryCodeTwo}">[[${column.countryName}]]</option>
                  </select>
                </div>
                <div class="form-group" id="withdraw2">
                  <label class="form-label required" th:text="#{withdraw.bankname}">银行名称</label>
                  <select class="form-control" name="bank_name" id="bank_name" style="display:none;">
                    <option th:each="column,columnStat:${bankList}" th:selected="${column eq mybank.bankName}" th:value="${column}">[[${column}]]</option>
                  </select>
                  <input type="text" class="form-control" id="bank_name3" name="bank_name3" th:value="${mybank.bankName}">
                </div>
                <div class="form-group" id="withdraw3">
                  <label class="form-label required" th:text="#{withdraw.bankaccount}">银行账户</label>
                  <input type="text" class="form-control" th:value="${mybank.bankAccount}" name="bank_num" id="bank_num">
                </div>
                <div class="form-group" id="withdraw4">
                  <label class="form-label required" th:text="#{withdraw.bankswift}">银行Swift/收款人</label>
                  <input type="text" class="form-control" th:value="${mybank.accountUsername}" name="withdrawal_name" id="withdrawal_name">
                </div>
                <div class="form-group full-width">
                  <label class="form-label" th:text="#{withdraw.notes}">备注说明</label>
                  <textarea class="form-control" name="remark" id="remark" rows="5"></textarea>
                </div>
              </div>
            </div>
            <button type="button" id="tjbutton" onclick="toWithdrawal();" class="submit-button" th:text="#{withdraw.submit}">提交出金申请</button>
          </form>
        </div>
      </div>

      <!-- 右侧：账户余额 + 最近出金 -->
      <div class="sidebar-content">
        <!-- 账户余额 -->
        <div class="sidebar-card">
          <h3 class="card-title">💳 <span th:text="#{deposit.accountbalance}">账户余额</span></h3>
          <div class="account-selector">
            <div class="account-balance-card" th:each="account,accountStat:${tradeList}" th:classappend="${accountStat.first} ? 'selected' : ''">
                <div class="account-id">💳[[${account.tradeId}]]</div>
              <div class="account-balance-label" th:text="#{deposit.availablebalance}">可用余额：</div>
              <div class="account-balance-amount">$[[${account.balance1}]]</div>
            </div>
          </div>
        </div>
        <!-- 最近出金记录（过滤type!=1） -->
        <div class="sidebar-card">
          <h3 class="card-title">📉 <span th:text="#{withdraw.recentwithdrawals}">最近出金</span></h3>
          <div class="recent-deposits-list withdraw-list">
            <div class="recent-deposit-item" th:each="cjl,accountStat:${cjList}">
              <div class="deposit-record-header">
                
                <span class="deposit-amount">$[[${#numbers.formatDecimal(cjl.amount,1,'COMMA',2,'POINT')}]]</span>
                <span class="deposit-status" th:classappend="${cjl.auditStatus == 0 ? 'pending' : (cjl.auditStatus == 1 ? 'success' : 'rejected')}">
                  <span th:if="${cjl.auditStatus == 0}" th:text="#{deposit.status.pending}">处理中</span>
                  <span th:if="${cjl.auditStatus == 1}" th:text="#{deposit.status.completed}">已完成</span>
                  <span th:if="${cjl.auditStatus == 2}" th:text="#{deposit.status.rejected}">已驳回</span>
                </span>
              </div>
              <div class="deposit-account">[[${cjl.tradeId}]]</div>
              <div class="deposit-date" th:data-timestamp="${cjl.gmtCreate.time}">[[${#dates.format(cjl.gmtCreate,'MM-dd HH:mm:ss')}]]</div>
            
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 隐藏表单（保持原有字段与出金流程） -->
  <form id="withdrawForm" method="POST">
    <input type="hidden" name="first_name" id="first_name" th:value="${userInfo.name}"/>
    <input type="hidden" name="last_name" id="last_name" th:value="${userInfo.surname}"/>
    <input type="hidden" name="email" id="email" th:value="${userInfo.userName}"/>
    <input type="hidden" name="postal_code" id="postal_code" th:value="${userInfo.country}"/>
    <input type="hidden" name="city" id="city" th:value="${userInfo.city}"/>
    <input type="hidden" name="address" id="address" th:value="${userInfo.adress}"/>
    <input type="hidden" name="notification_url" id="notification_url" value="https://seventy2u.seventybrokers.com/trade/payResult"/>
    <input type="hidden" name="return_url" id="return_url" value="https://seventy2u.seventybrokers.com/trade/deposit"/>
    <input type="hidden" name="login_id" id="login_id" value=""/>
    <input type="hidden" name="currency" id="currency" value=""/>
    <input type="hidden" name="amount" id="amount" value=""/>
    <input type="hidden" name="serial_number" id="serial_number" value=""/>
    <input type="hidden" name="sign" id="sign" value=""/>
    <input type="hidden" name="bank_name" id="bank_name2" value=""/>
    <input type="hidden" name="bank_courty" id="bank_courty2" value=""/>
    <input type="hidden" name="bank_swift" id="bank_swift2" value=""/>
    <input type="hidden" name="bank_account" id="bank_account2" value=""/>
    <input type="hidden" name="KYC_status" id="KYC_status" th:value="${userInfo.isAvailable}"/>
    <input type="hidden" name="ID_No" id="ID_No" th:value="${userInfo.identityNum}"/>
  </form>

  <!-- 页脚（统一规范） -->
  <footer class="footer footer-transparent d-print-none">
    <div style="max-width: 1200px; margin: 0 auto; padding: 20px; text-align: center;">
      <div style="color: rgba(255, 255, 255, 0.8); font-size: 14px;">
        ©Copyright 2025 All Rights Reserved. v[[${CRM_VERSION}]] - Licensed to [[${CRM_LICENSED}]]
      </div>
    </div>
  </footer>

  <!-- 隐藏的语言表单 -->
  <form class="card card-md" id="languageForm" method="post" style="display: none;">
    <input type="hidden" name="planguage" id="planguage"/>
  </form>

  <!-- Libs JS -->
  <script src="../dist/libs/apexcharts/dist/apexcharts.min.js?**********" defer></script>
  <script src="../dist/libs/jsvectormap/dist/js/jsvectormap.min.js?**********" defer></script>
  <script src="../dist/libs/jsvectormap/dist/maps/world.js?**********" defer></script>
  <script src="../dist/libs/jsvectormap/dist/maps/world-merc.js?**********" defer></script>
  <!-- Tabler Core -->
  <script src="../dist/js/tabler.min.js?**********" defer></script>
  <script src="../dist/js/demo.min.js?**********" defer></script>

  <!-- 保留原出金逻辑脚本 -->
  <script th:inline="javascript">
    var dqye=0; var djje=0;
    function selectTradeID(){
      $.ajax({type: "POST", url: "getTradeBalance?trade_id="+$("#trade_id").val(), success: function (result) {
        if (result!="false") {
          document.getElementById("dqye").innerHTML="";
          var json = eval('(' + result + ')');
          dqye=json.balance1; djje=json.balance6;
          document.getElementById("dqye").innerHTML="Max Withdrawable Cash: "+(json.balance1-json.balance6)+" "+"(On hold："+json.balance6+")";
        }
      }});
    }
    function toWithdrawal(){
      if(document.getElementById("withdrawalAmount").value==""){
        layer.open({style:'margin-bottom : 70%;',content:'Please fill all required items',skin:'msg',time:5000,btn:['OK'],title:'INFO'});return false;
      }
      if(document.getElementById("withdrawalAmount").value*1<=0){
        layer.open({style:'margin-bottom : 70%;',content:'The withdrawal amount must be greater than 0!',skin:'msg',time:5000,btn:['OK'],title:'INFO'});return false;
      }
      var bl=false;
      if($("#withdraw_currency").val()=="BTC"||$("#withdraw_currency").val()=="USDT TRC20"||$("#withdraw_currency").val()=="USDT ERC20"){
      }else{
        if(document.getElementById("bank_name").value==""&&document.getElementById("bank_name3").value==""){ bl=true; }
        if(bl||document.getElementById("bank_num").value==""||document.getElementById("withdrawal_name").value==""||document.getElementById("tel").value==""){
          layer.open({style:'margin-bottom : 70%;',content:'[[#{withdraw.prompt2}]]',skin:'msg',time:5000,btn:['OK'],title:'INFO'});return false;
        }
      }
      if(!isNumber(document.getElementById("withdrawalAmount").value)){
        layer.open({style:'margin-bottom : 70%;',content:'[[#{withdraw.prompt3}]]',skin:'msg',time:5000,btn:['OK'],title:'INFO'});return false;
      }
      if(document.getElementById("withdrawalAmount").value*1>((dqye-djje).toFixed(2)*1)){
        layer.open({style:'margin-bottom : 70%;',content:'[[#{withdraw.prompt4}]]:  '+((dqye-djje).toFixed(2)*1)+'  ',skin:'msg',time:5000,btn:['OK'],title:'INFO'});return false;
      }
      $("#tjbutton").attr('disabled',true);
      document.getElementById("currency").value=document.getElementById("withdraw_currency").value;
      document.getElementById("login_id").value=document.getElementById("trade_id").value;
      if(document.getElementById("tel").value=="MY"||document.getElementById("tel").value=="ID"||document.getElementById("tel").value=="VN"||document.getElementById("tel").value=="TH"){
        document.getElementById("bank_name2").value=document.getElementById("bank_name").value;
      }else{
        document.getElementById("bank_name2").value=document.getElementById("bank_name3").value;
      }
      document.getElementById("bank_courty2").value=document.getElementById("tel").value;
      document.getElementById("bank_account2").value=document.getElementById("bank_num").value;
      document.getElementById("bank_swift2").value=document.getElementById("withdrawal_name").value;
      $.ajax({type:"POST",url:"saveWithdrawInfo.do",data:$('#logonForm').serialize(),success:function(result){
        if("false"==result){
          layer.open({style:'margin-bottom : 70%;',content:'Failed',skin:'msg',time:5000,btn:['OK'],title:'INFO',end:function(){location.href="withdraw";}});
        }else{
          var json22 = eval('(' + result + ')');
          document.getElementById("amount").value=json22.amount;
          document.getElementById("serial_number").value=json22.serial_number;
          document.getElementById("sign").value=json22.sign;
          document.getElementById("login_id").value=json22.login_id;
          document.getElementById("withdrawForm").action=json22.action;
          toPostSubmit(json22.action);
        }
      },error:function(){
        layer.open({style:'margin-bottom : 70%;',content:'Error',skin:'msg',time:5000,btn:['OK'],title:'INFO',end:function(){location.href="withdraw";}});
      }});
    }
    selectTradeID();
    function isNumber(val){var regPos=/^\d+(\.\d+)?$/;var regNeg=/^(-(([0-9]+\.[0-9]*[1-9][0-9]*)|([0-9]*[1-9][0-9]*\.[0-9]+)|([0-9]*[1-9][0-9]*)))$/;return regPos.test(val)||regNeg.test(val);} 
    function toPostSubmit(url){
      $.ajax({type:"POST",url:url,data:$('#withdrawForm').serialize(),success:function(result) {}});
      layer.open({style:'margin-bottom : 70%;',content:'[[#{ext.prompt15}]]',skin:'msg',time:5000,btn:['OK'],title:'INFO',end:function(){location.href="withdraw";}});
    }
    function selectBankCountry(){
      $("#bank_name").html("");
      if(document.getElementById("tel").value=="MY"){
        $("#bank_name3").hide();$("#bank_name").show();
        var json = eval( [[${bankList_mala}]] );
        for(var m=0;m<json.length;m++){ $("#bank_name").append('<option value="'+json[m]+'">'+json[m]+'</option>'); }
      }else if(document.getElementById("tel").value=="ID"){
        $("#bank_name3").hide();$("#bank_name").show();
        var json = eval( [[${bankList_ind}]] );
        for(var m=0;m<json.length;m++){ $("#bank_name").append('<option value="'+json[m]+'">'+json[m]+'</option>'); }
      }else if(document.getElementById("tel").value=="TH"){
        $("#bank_name3").hide();$("#bank_name").show();
        var json = eval( [[${bankList_thai}]] );
        for(var m=0;m<json.length;m++){ $("#bank_name").append('<option value="'+json[m]+'">'+json[m]+'</option>'); }
      }else if(document.getElementById("tel").value=="VN"){
        $("#bank_name3").hide();$("#bank_name").show();
        var json = eval( [[${bankList_vitn}]] );
        for(var m=0;m<json.length;m++){ $("#bank_name").append('<option value="'+json[m]+'">'+json[m]+'</option>'); }
      }else{
        $("#bank_name").hide();$("#bank_name3").show();
      }
    }
    selectBankCountry();
    if(document.getElementById("tel").value=="MY"||document.getElementById("tel").value=="ID"||document.getElementById("tel").value=="VN"||document.getElementById("tel").value=="TH"){
      $("#bank_name").val([[${mybank.bankName}]]);
    }else{ $("#bank_name3").val([[${mybank.bankName}]]); }
    function selectCurrency(){
      if($("#withdraw_currency").val()=="BTC"||$("#withdraw_currency").val()=="USDT TRC20"||$("#withdraw_currency").val()=="USDT ERC20"){
        $("#withdraw1,#withdraw2,#withdraw3,#withdraw4").hide(); $("#withdraw5").show();
      }else{ $("#withdraw1,#withdraw2,#withdraw3,#withdraw4").show(); $("#withdraw5").hide(); }
    }
  </script>
  <script th:inline="javascript">
    function updateaLang(obj){
      document.getElementById("planguage").value=obj;
      $.ajax({type:"POST",url:"saveLanguage.do",data:$('#languageForm').serialize(),success:function(result){location.reload();},error:function(){}});
    }
    function toAlert(){ layer.open({style:'margin-bottom : 70%;',content:'[[#{ext.prompt1}]]',skin:'msg',time:5000,btn:['OK'],title:'INFO'}); }
    document.getElementById("trade_id").value=[[${param.id}]];
  </script>
  <!-- 移动端菜单交互（与入金页一致） -->
  <script>
    function toggleMobileMenu(){
      var mobileMenu=document.getElementById('mobileMenu');
      if(!mobileMenu){return;}
      if(mobileMenu.classList.contains('show')){ mobileMenu.classList.remove('show'); }
      else{ mobileMenu.classList.add('show'); }
    }
    document.addEventListener('click',function(e){
      var mobileMenu=document.getElementById('mobileMenu');
      var btn=document.querySelector('.mobile-menu-btn');
      if(!mobileMenu||!btn){return;}
      if(!mobileMenu.contains(e.target) && !btn.contains(e.target)){
        mobileMenu.classList.remove('show');
      }
    });
  </script>
  <!-- 相对时间显示（与入金记录一致，多语言支持） -->
  <script th:inline="javascript">
    function formatRelativeTime(timestamp){
      const now=new Date().getTime();
      const diff=now-timestamp;
      const minutes=Math.floor(diff/(1000*60));
      const hours=Math.floor(diff/(1000*60*60));
      const days=Math.floor(diff/(1000*60*60*24));
      const lang=document.documentElement.lang||'zh';
      const timeLabels={
        'zh':{now:'刚刚',minutesAgo:'分钟前',hoursAgo:'小时前',daysAgo:'天前',monthsAgo:'个月前'},
        'en':{now:'just now',minutesAgo:'minutes ago',hoursAgo:'hours ago',daysAgo:'days ago',monthsAgo:'months ago'},
        'ja':{now:'たった今',minutesAgo:'分前',hoursAgo:'時間前',daysAgo:'日前',monthsAgo:'ヶ月前'},
        'th':{now:'เมื่อสักครู่',minutesAgo:'นาทีที่แล้ว',hoursAgo:'ชั่วโมงที่แล้ว',daysAgo:'วันที่แล้ว',monthsAgo:'เดือนที่แล้ว'},
        'vi':{now:'vừa xong',minutesAgo:'phút trước',hoursAgo:'giờ trước',daysAgo:'ngày trước',monthsAgo:'tháng trước'},
        'ms':{now:'baru sahaja',minutesAgo:'minit lalu',hoursAgo:'jam lalu',daysAgo:'hari lalu',monthsAgo:'bulan lalu'},
        'in':{now:'baru saja',minutesAgo:'menit lalu',hoursAgo:'jam lalu',daysAgo:'hari lalu',monthsAgo:'bulan lalu'}
      };
      const labels=timeLabels[lang]||timeLabels['zh'];
      if(minutes<1){return labels.now;}
      else if(minutes<60){return minutes+labels.minutesAgo;}
      else if(hours<24){return hours+labels.hoursAgo;}
      else if(days<30){return days+labels.daysAgo;}
      else{const months=Math.floor(days/30);return months+labels.monthsAgo;}
    }
    function initRelativeTime(){
      const dateElements=document.querySelectorAll('.deposit-date[data-timestamp]');
      dateElements.forEach(function(element){
        const timestamp=parseInt(element.getAttribute('data-timestamp'));
        if(timestamp){ element.textContent=formatRelativeTime(timestamp); }
      });
    }
    document.addEventListener('DOMContentLoaded',function(){
      initRelativeTime();
      setInterval(initRelativeTime,60000);
    });
  </script>
  <!-- 语言下拉菜单初始化（避免不弹出的问题，与 deposit 同步） -->
  <script>
    $(document).ready(function(){
      $('.dropdown-toggle').on('click', function(e){
        e.preventDefault(); e.stopPropagation();
        var $dropdown = $(this).closest('.dropdown');
        var $menu = $dropdown.find('.dropdown-menu');
        $('.dropdown-menu').not($menu).removeClass('show');
        $menu.toggleClass('show');
      });
      $(document).on('click', function(e){
        if (!$(e.target).closest('.dropdown').length) {
          $('.dropdown-menu').removeClass('show');
        }
      });
    });
  </script>
  <script src="https://cdn.customgpt.ai/js/chat.js" th:inline="javascript" th:if="${FOOTER2_SCRIPT_STATUS} eq '1'"></script>
  <script th:if="${FOOTER2_SCRIPT_STATUS} eq '1'" th:inline="javascript">window.onload = function () { CustomGPT.init({p_id: [[${JS2_ID}]], p_key: [[${JS2_KEY}]], reset_conversation: "1"}); };</script>
  <!-- Start of seventyinvestech Zendesk Widget script -->
  <script id="ze-snippet" th:src="@{'https://static.zdassets.com/ekr/snippet.js?key='+${JS_KEY}}" th:if="${FOOTER_SCRIPT_STATUS} eq '1'"> </script>
  <!-- End of seventyinvestech Zendesk Widget script -->
</body>
</html>
