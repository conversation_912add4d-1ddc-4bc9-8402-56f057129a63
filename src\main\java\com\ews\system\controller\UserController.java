package com.ews.system.controller;

import java.awt.image.BufferedImage;
import java.io.ByteArrayOutputStream;
import java.io.File;
import java.io.FileOutputStream;
import java.text.DecimalFormat;
import java.text.ParseException;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Map;

import javax.imageio.ImageIO;
import javax.servlet.http.HttpServletRequest;
import javax.servlet.http.HttpServletResponse;
import javax.validation.Valid;

import org.apache.poi.hssf.usermodel.HSSFCell;
import org.apache.poi.hssf.usermodel.HSSFCellStyle;
import org.apache.poi.hssf.usermodel.HSSFClientAnchor;
import org.apache.poi.hssf.usermodel.HSSFPatriarch;
import org.apache.poi.hssf.usermodel.HSSFRichTextString;
import org.apache.poi.hssf.usermodel.HSSFRow;
import org.apache.poi.hssf.usermodel.HSSFSheet;
import org.apache.poi.hssf.usermodel.HSSFWorkbook;
import org.apache.shiro.SecurityUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.util.StringUtils;
import org.springframework.validation.BindingResult;
import org.springframework.web.bind.annotation.GetMapping;
import org.springframework.web.bind.annotation.PostMapping;
import org.springframework.web.bind.annotation.RequestBody;
import org.springframework.web.bind.annotation.RequestMapping;
import org.springframework.web.bind.annotation.RequestMethod;
import org.springframework.web.bind.annotation.RequestParam;
import org.springframework.web.bind.annotation.ResponseBody;
import org.springframework.web.bind.annotation.RestController;
import org.springframework.web.multipart.MultipartFile;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.common.EncryptUtils;
import com.ews.common.LoginResult;
import com.ews.common.SendCloudAPIV2;
import com.ews.config.ConstantConfig;
import com.ews.config.result.ResponseData;
import com.ews.config.result.ResponseDataUtil;
import com.ews.config.result.ResultEnums;
import com.ews.crm.controller.UserInfoController;
import com.ews.crm.entity.AccountType;
import com.ews.crm.entity.CompanyInfo;
import com.ews.crm.entity.EmailInfo;
import com.ews.crm.entity.FundInfo;
import com.ews.crm.entity.OperLog;
import com.ews.crm.entity.RuleUserMap;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.entity.TransferInfo;
import com.ews.crm.entity.UserInfo;
import com.ews.crm.service.CompanyInfoService;
import com.ews.crm.service.DataSqlService;
import com.ews.crm.service.EmailInfoService;
import com.ews.crm.service.FundInfoService;
import com.ews.crm.service.OperLogService;
import com.ews.crm.service.RuleUserMapService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.TransferInfoService;
import com.ews.crm.service.UserInfoService;
import com.ews.system.entity.Enclosure;
import com.ews.system.entity.LoginUser;
import com.ews.system.entity.Role;
import com.ews.system.entity.User;
import com.ews.system.entity.UserRole;
import com.ews.system.entity.UserTree;
import com.ews.system.model.IUserRole;
import com.ews.system.service.EnclosureService;
import com.ews.system.service.LoginService;
import com.ews.system.service.PermissionService;
import com.ews.system.service.RoleService;
import com.ews.system.service.UserRoleService;
import com.ews.system.service.UserService;

@RestController
@RequestMapping("/admin/user")
public class UserController {
	@Autowired
	private UserService userService;
	
	@Autowired
	private FundInfoService fundInfoService;

	@Autowired
	LoginService loginService;
	@Autowired
	RoleService roleService;
	@Autowired
	UserRoleService userRoleService;

	@Autowired
	PermissionService permissionService;

	@Autowired
	private ConstantConfig constantConfig;
	

	@Autowired
	private EnclosureService enclosureService;
	
	@Autowired
	private UserInfoService userInfoService;
	@Autowired
	private TradeAccountService tradeAccountService;
	
	@Autowired
	private RuleUserMapService ruleUserMapService;
	
	@Autowired
	private OperLogService operLogService;
	
	@Autowired
	private TransferInfoService transferInfoService;
	
	@Autowired
	private DataSqlService dataSqlService;
	@Autowired
	private EmailInfoService emailInfoService;
	
	@Autowired
	private CompanyInfoService companyInfoService;

	/**
	 * 用户登录
	 * 
	 * @param map
	 * @param data
	 * @return
	 */
	@PostMapping("/login")
	public ResponseData login(@RequestBody JSONObject data) {
		try {
			if (data.containsKey("username") && data.containsKey("password")) {
				String username = data.getString("username");
				String password = data.getString("password");

				LoginResult loginResult = loginService.login(username, password);
				if (loginResult.isLogin()) {
					JSONObject jo = new JSONObject();
					jo.put("token", loginResult.getResult());
					return ResponseDataUtil.buildSuccess(jo);
				} else {
					return ResponseDataUtil.buildError(loginResult.getResult());
				}
			} else {
				return ResponseDataUtil.buildLoginError();
			}

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}


	
	/**
	 * 登录后获取用户基本信息
	 * 
	 * @param map
	 * @return
	 */
	@GetMapping("/info")
	public ResponseData info(Map<String, Object> map) {
		try {
			// String token = SecurityUtils.getSubject().getPrincipal().toString();
			String username = this.loginService.getCurrentUserName();
			if (!StringUtils.hasText(username)) {
				System.out.println("登录超时    " + username);
				return ResponseDataUtil.buildError(ResultEnums.LOGIN_TIMEOUT_ERROR);
			}
			LoginUser user = this.userService.findLoginUserByUserName(username);
			if (user != null) {
				return ResponseDataUtil.buildSuccess(user);
			} else {
				return ResponseDataUtil.buildError(ResultEnums.LOGIN_TIMEOUT_ERROR);
			}
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}

	@RequestMapping(value = "/updateSelf")
	public ResponseData updateSelf(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
			if (data.containsKey("userId")) {
				String userName = this.loginService.getCurrentUserName();
				User user = this.userService.findByUserName(userName);
				if (user != null && user.getUserId().longValue() == Long.parseLong(data.getString("userId"))) {
					if (data.containsKey("mobile")) {
						user.setMobile(data.getString("mobile"));
					}
					if (data.containsKey("nickName")) {
						user.setNickName(data.getString("nickName"));
					}
					if (data.containsKey("userIcon")) {
						user.setUserIcon(data.getString("userIcon"));
					}

					
					
					List<UserRole> urs = userRoleService.findByUserId(user.getUserId());
					String[] roleIdsStr = null;
					roleIdsStr = new String[urs.size()];
					
					for (int i = 0; i < urs.size(); i++) {
						roleIdsStr[i] = urs.get(i).getRoleId().toString();
					}
					
					Map r = userService.checkExistedByIdAndUserNameAndEmail(user.getUserId(), user.getUsername(),
							user.getEmail());
					if (r.get("existed").equals("false")) {
						User u = userService.saveOrUpdate(user, roleIdsStr);
						if (u != null) {
							return ResponseDataUtil.buildSuccess();
						} else {
							return ResponseDataUtil.buildError("Update failed");
						}
					} else {
						if (r.get("type").equals("1")) {// 用户名重复
							return ResponseDataUtil.buildError("User already exists");
						} else if (r.get("type").equals("2")) {
							return ResponseDataUtil.buildError("Email already exists");
						} else {
							return ResponseDataUtil.buildError("Unexpected validation results " + r.get("type"));
						}
					}
				} else {
					return ResponseDataUtil.buildError("parameter error");
				}
			} else {
				return ResponseDataUtil.buildError("parameter error");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}

	/**
	 * 退出登录
	 * 
	 * @param map
	 * @param token
	 * @return
	 */
	@RequestMapping(value = "/logout")
	public ResponseData logout() {
		try {
			// 退出登录
			SecurityUtils.getSubject().logout();
			return ResponseDataUtil.buildSuccess();
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError("60204", "System error, please try again later");
		}
	}

	/**
	 * 刷新token
	 * 
	 * @param map
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/resetToken")
	public Object resetToken(Map<String, Object> map, @RequestBody JSONObject data) {

		JSONObject result = new JSONObject();
		try {
			String username = data.getString("username");
			String password = data.getString("password");

			System.out.println("username:" + username);
			System.out.println("password:" + password);

			LoginResult loginResult = loginService.login(username, password);
			System.out.println("loginResult:" + loginResult.getResult());

			if (loginResult.isLogin()) {
				result.put("code", 20000);
				JSONObject token = new JSONObject();
				token.put("token", "admin-token");
				result.put("data", token);
			} else {
				result.put("code", 60204);
				result.put("message", "the username or password is incorrect");
			}
		} catch (Exception e) {
			e.printStackTrace();
			result.put("code", 60204);
			result.put("message", "System error, please try again later!");
		}
		return result;
	}

	/**
	 * 用户查询.
	 * 
	 * @return
	 */
	@PostMapping("/list")
	public ResponseData userList(HttpServletRequest request) {
		try {
			Integer page = 0;
			Integer limit =10;
			String sort = request.getParameter("sort");
			// 获取分页控件的信息
			String sortBy = "desc";

			if(!request.getParameter("page").isEmpty()) {
				page = Integer.parseInt(request.getParameter("page"));
			}
			if(!request.getParameter("limit").isEmpty()) {
				limit = Integer.parseInt(request.getParameter("limit"));
			}
			
			if (sort != null) {
				if (sort.startsWith("+")) {
					sortBy = "asc";
				}
				sort = sort.replace("+", "").replace("-", "");
			}
			
			User query = new User();
			query.setGmtCreateBegin(request.getParameter("gmtCreateBegin"));
			query.setGmtCreateEnd(request.getParameter("gmtCreateEnd"));
			query.setUsername(request.getParameter("username"));
			query.setNickName(request.getParameter("nickName"));
			
			
			if (!StringUtils.isEmpty(request.getParameter("isAvailable"))) {
				query.setIsAvailable(Integer.parseInt(request.getParameter("isAvailable")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("storeId"))) {
				query.setStoreId(Long.parseLong(request.getParameter("storeId")));
			}
			if (!StringUtils.isEmpty(request.getParameter("departmentId"))) {
				query.setDepartmentId(Long.parseLong(request.getParameter("departmentId")));
			}
			
			
			if (!StringUtils.isEmpty(request.getParameter("parent_ID"))) {
       		 User loginUsersss = this.userService.findByUserName(request.getParameter("parent_ID"));
       		 query.setReId(loginUsersss.getUserId()) ;
          	}
			
			
			  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			 //1.查出自己及名下所有的代理
	            			 query.setSortStr(uu.getSortStr());
	            		 }
	            	 }else {
		            	 query.setSortStr("998828282837748373738");
		             }
	             }else {
	            	 query.setSortStr("998828282837748373738");
	             }

			Page<User> pages = userService.findAll(page - 1, limit, sort, sortBy, query);

			List<LoginUser> userModelList = new ArrayList();
			for (int i = 0; i < pages.getContent().size(); i++) {
				User user = pages.getContent().get(i);
				LoginUser um = new LoginUser();
				List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
				List<String> roles = new ArrayList();
				for (IUserRole urm : iurList) {
					roles.add(urm.getRole());
				}
				um.setUserId(user.getUserId());
				um.setGmtCreateDate(user.getGmtCreate());
				um.setNickName(user.getNickName());
				um.setUsername(user.getUsername());
				um.setRolesNameArr(roles);
				um.setIsAvailable(user.getIsAvailable());
				um.setMobile(user.getMobile());
				um.setGmtCreateDateLong(um.getGmtCreateDate().getTime());
				um.setReId(user.getReId());
				um.setRoleType(user.getRoleType());
				um.setUserIcon(user.getUserIcon());
				um.setSaleSerial(user.getSaleSerial());
				um.setStoreId(user.getStoreId());
				um.setSort((page - 1) * limit + (i + 1));
				
				
				RuleUserMap rgm_query=new RuleUserMap();
				rgm_query.setUserId(um.getUserId());
				Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
				
				um.setRuleList(rgm_p.getContent());
				
				if(user.getReId()!=null&&user.getReId().intValue()!=0&&user.getReId().intValue()!=1) {
					
					User parent_user=(User)this.userService.findUserById(user.getReId());
					
					if(parent_user!=null) {
						um.setStoreName(parent_user.getUsername()+"("+parent_user.getNickName()+")");
						um.setDepaName(parent_user.getUsername());
					}
					
				}else if(user.getReId().intValue()==1) {
					um.setStoreName(" ");
					um.setDepaName("");
				}
				userModelList.add(um);
			}
			JSONObject datas = new JSONObject();
			datas.put("items", userModelList);
			datas.put("total", pages.getTotalElements());

			return ResponseDataUtil.buildSuccess(datas);

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}

	}
	
	
	/**
	 * 用户查询.
	 * 
	 * @return
	 */
	@PostMapping("/list2")
	public ResponseData userList2(HttpServletRequest request) {
		try {
			Integer page = 0;
			Integer limit =10;
			String sort = request.getParameter("sort");
			// 获取分页控件的信息
			String sortBy = "desc";

			if(!request.getParameter("page").isEmpty()) {
				page = Integer.parseInt(request.getParameter("page"));
			}
			if(!request.getParameter("limit").isEmpty()) {
				limit = Integer.parseInt(request.getParameter("limit"));
			}
			
			if (sort != null) {
				if (sort.startsWith("+")) {
					sortBy = "asc";
				}
				sort = sort.replace("+", "").replace("-", "");
			}
			
			User query = new User();
			query.setGmtCreateBegin(request.getParameter("gmtCreateBegin"));
			query.setGmtCreateEnd(request.getParameter("gmtCreateEnd"));
			query.setUsername(request.getParameter("username"));
			query.setNickName(request.getParameter("nickName"));
			
			
			if (!StringUtils.isEmpty(request.getParameter("isAvailable"))) {
				query.setIsAvailable(Integer.parseInt(request.getParameter("isAvailable")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("storeId"))) {
				query.setStoreId(Long.parseLong(request.getParameter("storeId")));
			}
			if (!StringUtils.isEmpty(request.getParameter("departmentId"))) {
				query.setDepartmentId(Long.parseLong(request.getParameter("departmentId")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("reId"))) {
				query.setReId(Long.parseLong(request.getParameter("reId")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("parent_ID"))) {
	       		 User loginUsersss = this.userService.findByUserName(request.getParameter("parent_ID"));
	       		 query.setReId(loginUsersss.getUserId()) ;
	          	}
			
			query.setRoleType(-99);
			
			
			  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			 //1.查出自己及名下所有的代理
	            			 query.setSortStr(uu.getSortStr());
	            		 }
	            	 }
	             }else {
	            	 query.setSortStr("998828282837748373738");
	             }

			Page<User> pages = userService.findAll(page - 1, limit, sort, sortBy, query);

			List<LoginUser> userModelList = new ArrayList();
			for (int i = 0; i < pages.getContent().size(); i++) {
				User user = pages.getContent().get(i);
				LoginUser um = new LoginUser();
				List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
				List<String> roles = new ArrayList();
				for (IUserRole urm : iurList) {
					roles.add(urm.getRole());
				}
				um.setUserId(user.getUserId());
				um.setGmtCreateDate(user.getGmtCreate());
				um.setNickName(user.getNickName());
				um.setUsername(user.getUsername());
				um.setRolesNameArr(roles);
				um.setIsAvailable(user.getIsAvailable());
				um.setMobile(user.getMobile());
				um.setGmtCreateDateLong(um.getGmtCreateDate().getTime());
				um.setReId(user.getReId());
				um.setRoleType(user.getRoleType());
				um.setUserIcon(user.getUserIcon());
				um.setSaleSerial(user.getSaleSerial());
				um.setStoreId(user.getStoreId());
				um.setSort((page - 1) * limit + (i + 1));
				
				if(user.getReId()!=null&&user.getReId().intValue()!=0&&user.getReId().intValue()!=1) {
					
					User parent_user=(User)this.userService.findUserById(user.getReId());
					
					if(parent_user!=null) {
						um.setStoreName(parent_user.getUsername()+"("+parent_user.getNickName()+")");
					}
					
				}else if(user.getReId().intValue()==1) {
					um.setStoreName(" ");
				}
				
				
				RuleUserMap rgm_query=new RuleUserMap();
				rgm_query.setUserId(um.getUserId());
				Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
				
				um.setRuleList(rgm_p.getContent());
				
			
				userModelList.add(um);
			}
			JSONObject datas = new JSONObject();
			datas.put("items", userModelList);
			datas.put("total", pages.getTotalElements());

			return ResponseDataUtil.buildSuccess(datas);

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}

	}
	
	/**
	 * 用户业绩.
	 * 
	 * @return
	 */
	@PostMapping("/list3")
	public ResponseData userList3(HttpServletRequest request) {
		try {
			Integer page = 0;
			Integer limit =10;
			String sort = request.getParameter("sort");
			// 获取分页控件的信息
			String sortBy = "desc";

			if(!request.getParameter("page").isEmpty()) {
				page = Integer.parseInt(request.getParameter("page"));
			}
			if(!request.getParameter("limit").isEmpty()) {
				limit = Integer.parseInt(request.getParameter("limit"));
			}
			
			if (sort != null) {
				if (sort.startsWith("+")) {
					sortBy = "asc";
				}
				sort = sort.replace("+", "").replace("-", "");
			}
			
			User query = new User();
			query.setGmtCreateBegin(request.getParameter("gmtCreateBegin"));
			query.setGmtCreateEnd(request.getParameter("gmtCreateEnd"));
			query.setUsername(request.getParameter("username"));
			query.setNickName(request.getParameter("nickName"));
			
			
			if (!StringUtils.isEmpty(request.getParameter("isAvailable"))) {
				query.setIsAvailable(Integer.parseInt(request.getParameter("isAvailable")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("storeId"))) {
				query.setStoreId(Long.parseLong(request.getParameter("storeId")));
			}
			if (!StringUtils.isEmpty(request.getParameter("departmentId"))) {
				query.setDepartmentId(Long.parseLong(request.getParameter("departmentId")));
			}
			
			if (!StringUtils.isEmpty(request.getParameter("reId"))) {
				query.setReId(Long.parseLong(request.getParameter("reId")));
			}
			
			query.setRoleType(-99);
			
			
			  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	             if(loginUser!=null) {
	            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	            	 if(uu!=null&&uu.getUserId()!=null) {
	            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	            			 //1.查出自己及名下所有的代理
	            			 query.setSortStr(uu.getSortStr());
	            		 }
	            	 }
	             }else {
	            	 query.setSortStr("998828282837748373738");
	             }

			Page<User> pages = userService.findAll(page - 1, limit, sort, sortBy, query);

			List<LoginUser> userModelList = new ArrayList();
			for (int i = 0; i < pages.getContent().size(); i++) {
				User user = pages.getContent().get(i);
				LoginUser um = new LoginUser();
				List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
				List<String> roles = new ArrayList();
				for (IUserRole urm : iurList) {
					roles.add(urm.getRole());
				}
				um.setUserId(user.getUserId());
				um.setGmtCreateDate(user.getGmtCreate());
				um.setNickName(user.getNickName());
				um.setUsername(user.getUsername());
				um.setRolesNameArr(roles);
				um.setIsAvailable(user.getIsAvailable());
				um.setMobile(user.getMobile());
				um.setGmtCreateDateLong(um.getGmtCreateDate().getTime());
				um.setReId(user.getReId());
				um.setRoleType(user.getRoleType());
				um.setUserIcon(user.getUserIcon());
				um.setSaleSerial(user.getSaleSerial());
				um.setStoreId(user.getStoreId());
				um.setSort((page - 1) * limit + (i + 1));
				
				
				RuleUserMap rgm_query=new RuleUserMap();
				rgm_query.setUserId(um.getUserId());
				Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
				
				um.setRuleList(rgm_p.getContent());
				
				
				
				//名下代理数
				User query_u = new User();
				query_u.setSortStr(user.getSortStr());
				Page<User> pages_u = userService.findAll(0, 10000, sort, sortBy, query_u);
				um.setAgentQty(pages_u.getContent().size());
				
				//名下CRM用户数
				
				
				 UserInfo query_ui=new UserInfo();
				 User user_query=new User();
    			 user_query.setSortStr(user.getSortStr());
    			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
    			 List userList=new ArrayList();
    			 userList.add(user.getUserId());
    			 for(int n=0;n<ul.getContent().size();n++) {
    				 User user_1=ul.getContent().get(n);
    				 userList.add(user_1.getUserId());
    			 }
    			 query_ui.setUserInfoList(userList);
    			 Page<UserInfo> pages_ui = userInfoService.findAll(0, 10000, "id", "asc", query_ui);
    			 um.setUserInfoQty(pages_ui.getContent().size());
    			 
    			 
    			 //根据CRM用户查出入金
    			 
    			 um.setFundAmountDepositTotal(0d);
    			 um.setFundAmountWithdrawTotal(0d);
    			 for(int mmm=0;mmm<pages_ui.getContent().size();mmm++) {
    				 UserInfo uu_ii=(UserInfo)pages_ui.getContent().get(mmm);
    				 Double temp_fdt=this.dataSqlService.getFundTotal(uu_ii.getId(),1);//入金
    				 Double temp_fwt=this.dataSqlService.getFundTotal(uu_ii.getId(),2);//出金
    				 
    				 if(um.getFundAmountDepositTotal()!=null) {
    					 um.setFundAmountDepositTotal(um.getFundAmountDepositTotal()+temp_fdt);
    				 }else {
    					 um.setFundAmountDepositTotal(temp_fdt);
    				 }
    				 
    				 
    				 if(um.getFundAmountWithdrawTotal()!=null) {
    					 um.setFundAmountWithdrawTotal(um.getFundAmountWithdrawTotal()+temp_fwt);
    					 
    					
    				 }else {
    					 um.setFundAmountWithdrawTotal(temp_fwt);
    				 }
    			 }
    			 
                  				
    			 System.out.println(um.getFundAmountWithdrawTotal());
				
				//名下交易账户数 及余额总值
    			 TradeAccount query_ta=new TradeAccount();
    			 List userList2=new ArrayList();
    			 for(int m1=0;m1<pages_ui.getContent().size();m1++) {
    				 UserInfo ui_2=(UserInfo)pages_ui.getContent().get(m1);
    				 userList2.add(ui_2.getId());
    			 }
    			 
    			 if(pages_ui.getContent().size()<=0) {
    				 userList2.add(999999L);
    			 }
    			 query_ta.setUserInfoList(userList2);
    			 Page<TradeAccount> pages_ta = tradeAccountService.findAll(0, 10000, "id", "asc", query_ta);
    			 DecimalFormat df3=new DecimalFormat("000.00");
    			 um.setTradeAccountAmount(0D);
    			 for(int mm=0;mm<pages_ta.getContent().size();mm++) {
    				 TradeAccount ttta=(TradeAccount)pages_ta.getContent().get(mm);
    				 
    				 if(ttta.getBalance1()!=null) {
    						 um.setTradeAccountAmount(new Double(df3.format(um.getTradeAccountAmount()+ttta.getBalance1())));
        				 
    				 }
    					 
    				 
    			 }
				
				um.setTradeAccountQty(pages_ta.getContent().size());
				userModelList.add(um);
				
				
				
				
				
			}
			JSONObject datas = new JSONObject();
			datas.put("items", userModelList);
			datas.put("total", pages.getTotalElements());

			return ResponseDataUtil.buildSuccess(datas);

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}

	}

	/**
	 * 添加;
	 * 
	 * @return
	 */
	@RequestMapping(value = "/add", method = RequestMethod.POST)
	public ResponseData add(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
			User user = new User();
			if(data.containsKey("username")) {
				user.setUsername(data.getString("username"));
			}
			
			if(data.containsKey("password")) {
				user.setPassword(data.getString("password"));
			}
			
			user.setPassword(data.getString("password"));
			user.setMobile(data.getString("mobile"));
			user.setNickName(data.getString("nickName"));
			user.setIsAvailable(data.getInteger("isAvailable"));
			if(data.containsKey("roleType")) {
				user.setRoleType(data.getInteger("roleType"));
			}
			if(data.containsKey("storeId")) {
				user.setStoreId(data.getLong("storeId"));
			}
			if(data.containsKey("reId")&&!data.getString("reId").equals("")) {
				user.setReId(data.getLong("reId"));
			}else {
				user.setReId(1L);
			}
			user.setSaleSerial(new UserInfoController().genRandomNum(10,1));
			
			if(data.containsKey("storeId")&&!data.getString("storeId").equals("")) {
				try {
					TradeAccount ta_query=new TradeAccount();
					ta_query.setTradeId(data.getString("storeId"));
					Page<TradeAccount> uinfo_page=this.tradeAccountService.findAll(0,1, "id","asc", ta_query);
					if(uinfo_page.getContent().size()>0) {
						UserInfo u_info=this.userInfoService.findById(uinfo_page.getContent().get(0).getUserId());
						user.setDepartmentId(u_info.getId());
					}
					
				}catch(Exception e) {
					
				}
			}
			if(data.containsKey("userIcon")) {
				user.setUserIcon(data.getString("userIcon"));
			}
			
			
			if(data.containsKey("enclosureId")) {
			Enclosure enclosure = this.enclosureService.findById(data.getLong("enclosureId"));
			if(enclosure!=null) {
				if(enclosure.getBusId()==null && enclosure.getBusType() == null) {
					enclosure.setBusId(user.getUserId());
					enclosure.setBusType(EnclosureService.USER);
					this.enclosureService.saveOrUpdate(enclosure);
				}
				user.setUserIcon(enclosure.getEndixUrl());
			}else {
				user.setUserIcon(null);
			}
			}
			String[] roleIdsStr = null;
			if (data.containsKey("roles")) {
				JSONArray ja = data.getJSONArray("roles");
				roleIdsStr = new String[ja.size()];
				for (int i = 0; i < ja.size(); i++) {
					roleIdsStr[i] = ja.get(i).toString();
				}
			}
			Map r = userService.checkExistedByIdAndUserNameAndEmail(user.getUserId(), user.getUsername(),
					user.getEmail());
			if (r.get("existed").equals("false")) {
				User u = userService.saveOrUpdate(user, roleIdsStr);
				u.setSortStr("$"+String.format("%010d",u.getUserId()));
				
				
				
				if(data.containsKey("parent_ID")&&!data.getString("parent_ID").equals("")) {
					
					LoginUser user_parent = this.userService.findLoginUserByUserName(data.getString("parent_ID"));
					if(user_parent!=null&&user_parent.getUserId()!=null) {
						u.setReId(user_parent.getUserId());
						if(user_parent.getUserId().intValue()==1) {
							u.setOrderStr("$"+String.format("%010d",1L));
							}else if(user_parent.getUserId()>1){
								User u_Parent=this.userService.findUserById(user_parent.getUserId());
								u.setOrderStr(u_Parent.getOrderStr()+u_Parent.getSortStr());
							}
					}
					
					
					
				}else {
					   if(u.getReId().intValue()==1) {
						u.setOrderStr("$"+String.format("%010d",1L));
						}else if(u.getReId()>1){
							User u_Parent=this.userService.findUserById(u.getReId());
							u.setOrderStr(u_Parent.getOrderStr()+u_Parent.getSortStr());
						}
					
				}
				
				this.userService.save(u);
				
				
				Object[] o1 = data.getJSONArray("rekebackRule").toArray();
				for(int i=0;i<o1.length;i++) {
					RuleUserMap rpm=new RuleUserMap();
					rpm.setUserId(u.getUserId());
					rpm.setRuleId(new Long(o1[i].toString()));
					this.ruleUserMapService.saveOrUpdate(rpm);
				}
				
				
				
				LoginUser um = new LoginUser();
				List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
				String role = "";
				for (IUserRole urm : iurList) {
					role = role + urm.getRole() + "\n";
				}
				um.setUserId(u.getUserId());
				um.setGmtCreateDate(u.getGmtCreate());
				um.setNickName(u.getNickName());
				um.setUsername(u.getUsername());
				um.setRoleName(role);
				um.setIsAvailable(u.getIsAvailable());
				um.setMobile(u.getMobile());
				um.setReId(u.getReId());
				um.setRoleType(u.getRoleType());
				um.setUserIcon(u.getUserIcon());
				um.setSaleSerial(u.getSaleSerial());
				
				
				//操作日志 -begin
				User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
				OperLog olog=new OperLog();
				olog.setBusId(um.getUserId());
				olog.setBusType(1);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
				olog.setOperId(loginUser_log.getUserId());
				olog.setOperName(loginUser_log.getUsername());
				olog.setOperType(1);  //1 新增   2 修改  3 删除  4 审批
				this.operLogService.saveOrUpdate(olog);
				//操作日志 -end

				//给代理发送邮件
				if(user.getUsername() != null && user.getUsername().matches("^[A-Za-z0-9._%+-]+@[A-Za-z0-9.-]+\\.[A-Z|a-z]{2,}$")){
					EmailInfo emailInfo=(EmailInfo)this.emailInfoService.findById(new Long(1L));
					CompanyInfo companyInfo=(CompanyInfo)this.companyInfoService.findById(new Long(1L));
					try {
						SendCloudAPIV2.send_template(emailInfo.getApiUser(),emailInfo.getApiKey(),"Agent Opening Notice","",user.getUsername(),data.getString("password"),emailInfo.getBackup7(),emailInfo.getFromEmail(),emailInfo.getEmailName() ,user.getUsername(),user.getNickName(),"");
					}catch(Exception e) {
						System.out.println(e);
					}
				}
			
			    
				
				return ResponseDataUtil.buildSuccess(um);
			} else {
				if (r.get("type").equals("1")) {// 用户名重复
					return ResponseDataUtil.buildError("User already exists");
				} else if (r.get("type").equals("2")) {
					return ResponseDataUtil.buildError("Email already exists");
				} else {
					return ResponseDataUtil.buildError("Unexpected validation results " + r.get("type"));
				}
			}

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}

	/**
	 * 更新
	 * @param request
	 * @param data
	 * @return
	 */
	@RequestMapping(value = "/update")
	public ResponseData update(HttpServletRequest request, @RequestBody JSONObject data) {
		try {
			if (data.containsKey("userId")) {
				Long userId = data.getLong("userId");
				User user = this.userService.findUserById(userId);
				if (user != null) {
					
					if(data.containsKey("enclosureId")) {
					Enclosure enclosure = this.enclosureService.findById(data.getLong("enclosureId"));
					if(enclosure!=null) {
						if(enclosure.getBusId()==null && enclosure.getBusType() == null) {
							enclosure.setBusId(user.getUserId());
							enclosure.setBusType(EnclosureService.USER);
							this.enclosureService.saveOrUpdate(enclosure);
						}
						user.setUserIcon(enclosure.getEndixUrl());
					}else {
						user.setUserIcon(null);
					}
					}
					
					
					
					user.setUsername(data.getString("username"));
					user.setMobile(data.getString("mobile"));
					user.setNickName(data.getString("nickName"));
					
					if(data.containsKey("departmentId")) {
						user.setDepartmentId(data.getLong("departmentId"));
					}
					
					
					if(data.containsKey("reId")) {
						if(data.getLong("reId")!=null&&!data.getString("reId").equals("")) {
							
							user.setReId(data.getLong("reId"));
						}else {
							user.setReId(1L);
						}
					}else {
						user.setReId(1L);
					}
					
					if(data.containsKey("roleType")) {
						user.setRoleType(data.getInteger("roleType"));
					}
					if(data.containsKey("storeId")) {
						user.setStoreId(data.getLong("storeId"));
					}
					if(data.containsKey("storeId")) {
						if(user.getStoreId()!=null) {
						try {
							TradeAccount ta_query=new TradeAccount();
							ta_query.setTradeId(data.getString("storeId"));
							Page<TradeAccount> uinfo_page=this.tradeAccountService.findAll(0,1, "id","asc", ta_query);
							if(uinfo_page.getContent().size()>0) {
								UserInfo u_info=this.userInfoService.findById(uinfo_page.getContent().get(0).getUserId());
								user.setDepartmentId(u_info.getId());
							}
							
						}catch(Exception e) {
							
						}
						}
					}
					
					if (data.containsKey("userIcon")) {
						user.setUserIcon(data.getString("userIcon"));
					}
					if(data.containsKey("isAvailable")) {
						user.setIsAvailable(data.getInteger("isAvailable"));
					}
					if(data.containsKey("saleSerial")) {
					user.setSaleSerial(data.getString("saleSerial"));
					}
					System.out.println("isAvailable:"+data.get("isAvailable"));
					
					
					
					if(data.containsKey("parent_ID")&&!data.getString("parent_ID").equals("")) {
						LoginUser user_parent = this.userService.findLoginUserByUserName(data.getString("parent_ID"));
						if(user_parent!=null&&user_parent.getUserId()!=null) {
							user.setReId(user_parent.getUserId());//重新设置上级ID
							//记录原orderStr串
							String sub_old=user.getOrderStr()+user.getSortStr();
							//拼接新的orderStr串
							User u_Parent=this.userService.findUserById(user_parent.getUserId());
							String order_new=u_Parent.getOrderStr()+u_Parent.getSortStr();
							String sub_new=order_new+user.getSortStr();
							user.setOrderStr(order_new);
							
							this.userService.save(user);
							User query_u = new User();
							query_u.setSortStr(sub_old);
							Page<User> pages_u = userService.findAll(0, 10000, "userId", "asc", query_u);
							
							for(int j=0;j<pages_u.getContent().size();j++) {
								User p_j=(User)pages_u.getContent().get(j);
								p_j.setOrderStr(p_j.getOrderStr().replace(sub_old, sub_new));
								this.userService.save(p_j);
								
							}
						}
					}
					
					RuleUserMap rgm_query=new RuleUserMap();
					rgm_query.setUserId(user.getUserId());
					Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
					for(int m=0;m<rgm_p.getContent().size();m++) {
						RuleUserMap alm=(RuleUserMap)rgm_p.getContent().get(m);
						this.ruleUserMapService.removeEntityById(alm.getId());
					}
					
					Object[] o1 = data.getJSONArray("rekebackRule").toArray();
					for(int i=0;i<o1.length;i++) {
						RuleUserMap rpm=new RuleUserMap();
						rpm.setUserId(user.getUserId());
						rpm.setRuleId(new Long(o1[i].toString()));
						this.ruleUserMapService.saveOrUpdate(rpm);
					}
					
					String[] roleIdsStr = null;
					if (data.containsKey("roles")) {
						JSONArray ja = data.getJSONArray("roles");
						roleIdsStr = new String[ja.size()];
						for (int i = 0; i < ja.size(); i++) {
							roleIdsStr[i] = ja.get(i).toString();
						}
					}
					Map r = userService.checkExistedByIdAndUserNameAndEmail(user.getUserId(), user.getUsername(),
							user.getEmail());
					if (r.get("existed").equals("false")) {
						User u = userService.saveOrUpdate(user, roleIdsStr);
						if (u != null) {
							
							//操作日志 -begin
							User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
							OperLog olog=new OperLog();
							olog.setBusId(u.getUserId());
							olog.setBusType(1);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
							olog.setOperId(loginUser_log.getUserId());
							olog.setOperName(loginUser_log.getUsername());
							olog.setOperType(2);  //1 新增   2 修改  3 删除  4 审批
							this.operLogService.saveOrUpdate(olog);
							//操作日志 -end
							return ResponseDataUtil.buildSuccess();
						} else {
							return ResponseDataUtil.buildError("Update failed");
						}
					} else {
						if (r.get("type").equals("1")) {// 用户名重复
							return ResponseDataUtil.buildError("User already exists");
						} else if (r.get("type").equals("2")) {
							return ResponseDataUtil.buildError("Email already exists");
						} else {
							return ResponseDataUtil.buildError("Unexpected validation results " + r.get("type"));
						}
					}
				} else {
					return ResponseDataUtil.buildError("parameter error");
				}
			} else {
				return ResponseDataUtil.buildError("parameter error");
			}
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}

	/**
	 * 改变用户状态
	 * 
	 * @param id
	 * @param type
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/updateIsAvailable", method = RequestMethod.GET)
	public ResponseData updateIsAvailable(@Valid Long userId, @Valid Integer isAvailable, HttpServletRequest request) {
		if (userId != null && isAvailable != null) {
			try {
				if (this.userService.updateIsAvailableById(userId, isAvailable)) {
					return ResponseDataUtil.buildSuccess();
				} else {
					return ResponseDataUtil.buildError("Update failed");
				}
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError("parameter error");
		}

	}

	/**
	 * 删除用户
	 * 
	 * @param id
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/removeUser", method = RequestMethod.GET)
	public ResponseData removeUser(Long userId, HttpServletRequest request) {

		if (userId != null) {
			try {
				
				
				List idArray=new ArrayList();
				idArray.add(userId);
				this.userService.deleteAllUserByUserIdList(idArray);
				
				   //if (this.userService.logicalDeleteByUserId(userId)) {
					//操作日志 -begin
					User loginUser_log = this.userService.findByUserName(this.loginService.getCurrentUserName());
					OperLog olog=new OperLog();
					olog.setBusId(userId);
					olog.setBusType(1);  //1 代理用户/后台用户  2 CRM用户  3入金 4 出金 5同名账号 6同名转账 7交易用户
					olog.setOperId(loginUser_log.getUserId());
					olog.setOperName(loginUser_log.getUsername());
					olog.setOperType(3);  //1 新增   2 修改  3 删除  4 审批
					this.operLogService.saveOrUpdate(olog);
					//操作日志 -end
					return ResponseDataUtil.buildSuccess();
				//} else {
					//return ResponseDataUtil.buildError("更新失败");
				//}
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError("parameter error");
		}

	}

	@RequestMapping(value = "/resetPassword", method = RequestMethod.POST)
	public ResponseData resetPassword(Long userId, String resetPassword) {
		if (userId != null && !StringUtils.isEmpty(resetPassword)) {
			try {
				if (this.userService.updatePasswordByUserId(userId, resetPassword)) {
					return ResponseDataUtil.buildSuccess();
				} else {
					return ResponseDataUtil.buildError("Update failed");
				}
			} catch (Exception e) {
				e.printStackTrace();
				return ResponseDataUtil.buildError(e.getMessage());
			}
		} else {
			return ResponseDataUtil.buildError("parameter error");
		}
	}

	/**
	 * 修改密码
	 * 
	 * @param request
	 * @return
	 */
	@RequestMapping(value = "/changePassword", method = RequestMethod.POST)
	@ResponseBody
	public ResponseData changePassword(@RequestBody JSONObject data) {
		Map<String, String> map = new HashMap<>();

		if (data.containsKey("password") && data.containsKey("newPassword") && data.containsKey("againPassword")) {
			String password = data.getString("password");
			String newPassword = data.getString("newPassword");
			String newPassword2 = data.getString("againPassword");

			if (!newPassword.equals(newPassword2)) {
				return ResponseDataUtil.buildError("The two password inputs are inconsistent");
			}
			if (newPassword.length() < 6) {
				return ResponseDataUtil.buildError("The password length cannot be less than 6 digits");
			}

			String userName = loginService.getCurrentUserName();
			if (userName == null || userName.isEmpty()) {
				return ResponseDataUtil.buildError("missing parameter");
			}
			User user = userService.findByUserName(userName);
			if (user == null) {
				return ResponseDataUtil.buildError(ResultEnums.LOGIN_TIMEOUT_ERROR);
			} else {
				String encryptPwd = new String();
				try {
					encryptPwd = EncryptUtils.encrypt(password, user.getCredentialsSalt());
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					return ResponseDataUtil.buildError(e.getMessage());
				}
				if (!encryptPwd.equals(user.getPassword())) {
					return ResponseDataUtil.buildError("Old password input error");
				}

				String encryptNewPwd = new String();
				try {
					encryptNewPwd = EncryptUtils.encrypt(newPassword, user.getCredentialsSalt());
				} catch (ParseException e) {
					// TODO Auto-generated catch block
					e.printStackTrace();
					return ResponseDataUtil.buildError(e.getMessage());
				}
				user.setPassword(encryptNewPwd);
				if (userService.save(user) == null) {
					return ResponseDataUtil.buildError("Update failed");
				} else {
					return ResponseDataUtil.buildSuccess("Successfully modified, please log in again");
				}
			}

		} else {
			return ResponseDataUtil.buildError("Password cannot be empty");
		}

	}

	@RequestMapping(value = "/toCheckPwd")
	@ResponseBody
	public ResponseData checkCurrentPwd(@RequestParam String password) {
		Map<String, Boolean> map = new HashMap<>();
		if (password == null) {
			return ResponseDataUtil.buildError("Password cannot be empty");
		}

		String userName = loginService.getCurrentUserName();
		if (userName == null || userName.isEmpty()) {
			return ResponseDataUtil.buildError("User logged in as");
		}
		User user = userService.findByUserName(userName);
		if (user == null) {
			return ResponseDataUtil.buildError("User information error");
		}

		String encryptPwd = new String();
		try {
			encryptPwd = EncryptUtils.encrypt(password, user.getCredentialsSalt());
		} catch (ParseException e) {
			// TODO Auto-generated catch block
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
		if (!encryptPwd.equals(user.getPassword())) {
			return ResponseDataUtil.buildError("Password error");
		}
		return ResponseDataUtil.buildSuccess();
	}

	/**
	 * 用户修个个人信息页面
	 * 
	 * @param id
	 * @param map
	 * @return
	 */
	@RequestMapping(value = "/toChangeUserInfo")
	public ResponseData toChangeUserInfo() {
		try {
			User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());// 获取当前登录用户的信息

			List<UserRole> urs = userRoleService.findByUserId(loginUser.getUserId());
			List<Role> roles = new ArrayList();
			for (UserRole ur : urs) {
				Role role = roleService.findById(ur.getRoleId());
				if (role.getIsAvailable().intValue() == 1) {
					roles.add(role);
				}
			}
			JSONObject data = new JSONObject();
			data.put("roles", roles);
			data.put("entity", loginUser);
			return ResponseDataUtil.buildSuccess(data);
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}
	}

	/**
	 * 用户自己编辑个人信息
	 * 
	 * @return
	 */
	@RequestMapping(value = "/changeUserInfoSave", method = RequestMethod.POST)
	@ResponseBody
	public Object changeUserInfoSave(@Valid User user, BindingResult bindingResult, HttpServletRequest request) {
		Map<String, String> map = new HashMap<>();
		if (bindingResult.hasErrors()) {
			map.put("success", "false");
			map.put("message", "parameter error");
			map.put("type", "1");
		}
		LoginUser loginUser = (LoginUser) SecurityUtils.getSubject().getPrincipal();

		if (loginUser == null || loginUser.getUserId().longValue() != user.getUserId().longValue()) {
			map.put("success", "false");
			map.put("message", "operation failed");
			map.put("type", "4");
			return map;
		}
		try {
			Map result = userService.checkExistedByIdAndUserNameAndEmail(user.getUserId(), user.getUsername(),
					user.getEmail());
			if (result.get("existed").equals("false")) {
				System.out.println(user.getNickName());
				List<UserRole> urs = userRoleService.findByUserId(user.getUserId());
				String[] roleIdsStr = null;
				roleIdsStr = new String[urs.size()];
				
				for (int i = 0; i < urs.size(); i++) {
					roleIdsStr[i] = urs.get(i).getRoleId().toString();
				}
				userService.saveOrUpdate(user, roleIdsStr);
				map.put("success", "true");
			} else {
				if (result.get("type").equals("1")) {// 用户名重复
					map.put("success", "false");
					map.put("message", "User name already exists");
					map.put("type", "2");
				} else if (result.get("type").equals("2")) {
					map.put("success", "false");
					map.put("message", "Email already exists");
					map.put("type", "3");
				} else {
					map.put("success", "false");
					map.put("message", "operation failed");
					map.put("type", "4");
				}
			}
			return map;
		} catch (Exception e) {
			e.printStackTrace();
			map.put("success", "false");
			map.put("message", "operation failed");
			map.put("type", "4");
			return map;
		}
	}

	/**
	 * 获取所有权限信息
	 * 
	 * @param map
	 * @param request
	 * @param response
	 * @return
	 */
	@RequestMapping(value = "/roles")
	public ResponseData roleList(Map<String, Object> map, HttpServletRequest request, HttpServletResponse response) {
		Role queryRole = new Role();
		queryRole.setIsAvailable(1);
		
		User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
		
		User curUser=this.userService.findUserById(loginUser.getUserId());
		if(curUser.getRoleType().intValue()!=999) {
			queryRole.setUserCreate(0L);
		}
		return ResponseDataUtil.buildSuccess(roleService.findAll(queryRole));
	}

	/**
	 * 获取指定用户的角色信息
	 * 
	 * @param map
	 * @param request
	 * @param response
	 * @return
	 */
	@GetMapping(value = "/userRoles")
	public ResponseData userRoles(Long userId) {
		if (userId == null) {
			return ResponseDataUtil.buildError("Required parameter missing");
		}
		return ResponseDataUtil.buildSuccess(roleService.findAllByUserId(userId));
	}

	/**
	 * 修改用户头像
	 * 
	 * @param request
	 * @param linkImg_temp
	 * @return
	 */

	@RequestMapping(value = "/uploadUserIcon")
	@ResponseBody
	public Object uploadUserIcon(HttpServletRequest request, MultipartFile file) {
		JSONObject result = new JSONObject();
		result.put("code", 20000);
		try {
			if (file != null) {
				String fileName = new Date().getTime() + "."
						+ file.getOriginalFilename().substring(file.getOriginalFilename().lastIndexOf(".") + 1);
				File reFile = new File(constantConfig.getFileStoreUrl());
				if (!reFile.exists()) {
					reFile.mkdirs();
				}
				File targetFile = new File(constantConfig.getFileStoreUrl() + fileName);

				System.out.println("文件保存相对路径：" + constantConfig.getFileStoreUrlPrefix() + fileName);
				file.transferTo(targetFile);// 保存文件
				JSONObject jo = new JSONObject();
				jo.put("userIcon", constantConfig.getFileStoreUrlPrefix() + fileName);
				result.put("data", jo);
			} else {
				result.put("code", 000000);
				result.put("message", "Unknown file");
			}

		} catch (Exception e) {
			e.printStackTrace();
			result.put("code", 000000);
			result.put("message", e.getMessage());
		}
		return result;

	}
	
	
	@GetMapping("/getSaleMgn")
	public ResponseData getSaleMgn(HttpServletRequest request) {
		try {
			User query = new User();
		    query.setRoleType(2);
			Page<User> pages = userService.findAll(0, 100, "userId", "asc", query);
			JSONObject datas = new JSONObject();
			datas.put("items", pages.getContent());
			return ResponseDataUtil.buildSuccess(datas);
		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}

	}
	
	@RequestMapping(value = "/bindCrmUser", method = RequestMethod.POST)
	public ResponseData bindCrmUser(HttpServletRequest request, @RequestBody JSONObject data) {
		
		if(data.containsKey("userId")&&data.containsKey("crm_username")&&!data.getString("crm_username").equals("")) {
			
			UserInfo ui_query=new UserInfo();
			ui_query.setUserName(data.getString("crm_username"));
			Page<UserInfo> ui_pages=this.userInfoService.findAll(0,1, "id","asc", ui_query);
			if(ui_pages.getContent().size()>0) {
				UserInfo ui=(UserInfo)ui_pages.getContent().get(0);
				User u=this.userService.findUserById(data.getLong("userId"));
				ui.setParentId(u.getUserId());
				this.userInfoService.saveOrUpdate(ui);
			}
		}
		return ResponseDataUtil.buildSuccess();
		
	}
	
	
	@GetMapping("/fetchUserTreeList")
	public ResponseData fetchUserTreeList(HttpServletRequest request) {
		
		
		
	    //System.out.println("userName:"+request.getParameter("userName"));
        
		List outList=new ArrayList();
        
	    try {
	    	
	    	
	    	if(request.getParameter("userName")!=null&&!request.getParameter("userName").equals("")&&request.getParameter("userName").equals("AllAgent")) {
	    		User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
	            if(loginUser!=null) {
	           	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
	           	 if(uu!=null&&uu.getUserId()!=null) {
	           		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
	           			 User user_query=new User();
	           			 user_query.setRoleType(-99);
	           		 UserTree ut=new UserTree();
                   	 ut.setName(uu.getUsername()+"("+uu.getNickName()+")");
                   	 ut.setValue(uu.getUserId());
                   	 user_query.setReId(uu.getUserId());
                   	 Page<User> user_list2=this.userService.findAll(0, 1000, "userId", "asc", user_query);
                   	 if(user_list2.getSize()>0)
                   	 {
                   		 ut.setLeaf(false);
                   		
                   	 }else {
                   		 ut.setLeaf(true);
                   	 }
                   	 outList.add(ut);
	           			   
	           		 }else {
	           			 
	           			 User user_query=new User();
	           			 user_query.setRoleType(-99);
	           			 user_query.setReId(1L);
                         Page<User> user_list=this.userService.findAll(0, 1000, "userId", "asc", user_query);	
                         for(int i=0;i<user_list.getContent().size();i++) {
                        	 UserTree ut=new UserTree();
                        	 ut.setName(user_list.getContent().get(i).getUsername()+"("+user_list.getContent().get(i).getNickName()+")");
                        	 ut.setValue(user_list.getContent().get(i).getUserId());
                        	 user_query.setReId(user_list.getContent().get(i).getUserId());
                        	 Page<User> user_list2=this.userService.findAll(0, 1000, "userId", "asc", user_query);
                        	 if(user_list2.getSize()>0)
                        	 {
                        		 ut.setLeaf(false);
                        		
                        	 }else {
                        		 ut.setLeaf(true);
                        	 }
                        	 outList.add(ut);
                         }
                         
	           		 }
	           	 }
	            }
	    		
	    	}else {
	    		 String bracket = request.getParameter("userName").substring(request.getParameter("userName").indexOf("("),request.getParameter("userName").indexOf(")")+1);
	    	     String thhuserName = request.getParameter("userName").replace(bracket, "");
	    		//System.out.println(thhuserName);
	    		
	    		User curUser = this.userService.findByUserName(thhuserName);
	    		
	    		User user_query=new User();
      			 user_query.setRoleType(-99);
      			 user_query.setReId(curUser.getUserId());
                Page<User> user_list=this.userService.findAll(0, 1000, "userId", "asc", user_query);	
                for(int i=0;i<user_list.getContent().size();i++) {
               	 UserTree ut=new UserTree();
               	 ut.setName(user_list.getContent().get(i).getUsername()+"("+user_list.getContent().get(i).getNickName()+")");
               	 ut.setValue(user_list.getContent().get(i).getUserId());
               	 user_query.setReId(user_list.getContent().get(i).getUserId());
               	 Page<User> user_list2=this.userService.findAll(0, 1000, "userId", "asc", user_query);
               	 if(user_list2.getSize()>0)
               	 {
               		 ut.setLeaf(false);
               		
               	 }else {
               		 ut.setLeaf(true);
               	 }
               	 outList.add(ut);
                }
	    		
	    		
	    	}
			  

			return ResponseDataUtil.buildSuccess(outList);

		} catch (Exception e) {
			e.printStackTrace();
			return ResponseDataUtil.buildError(e.getMessage());
		}

	}
	@PostMapping("/indexInfo")
    public ResponseData indexInfo(HttpServletRequest request) {
    	try {
    		UserInfo ui_query=new UserInfo();
    		ui_query.setIsAvailable(3);
        	Page<UserInfo> pages_ui = userInfoService.findAll(0,10000,"id", "asc", ui_query);
        	
        	FundInfo fi_query=new FundInfo();
        	fi_query.setType(1);
        	fi_query.setAuditStatus(0);
        	fi_query.setIsAvailable(1);
        	Page<FundInfo> pages_deposit = fundInfoService.findAll(0, 10000,"id", "asc", fi_query);
        	
        	
        	FundInfo fi_query2=new FundInfo();
        	fi_query2.setType(2);
        	fi_query2.setAuditStatus(0);
        	//fi_query2.setIsAvailable(1);
        	fi_query2.setBackup3(2);
        	Page<FundInfo> pages_withdraw = fundInfoService.findAll(0, 10000,"id", "asc", fi_query2);
        	
        	TradeAccount ta_query=new TradeAccount();
        	ta_query.setIsAvailable(0);
           	Page<TradeAccount> pages_ta = tradeAccountService.findAll(0, 10000,"id", "asc", ta_query);
           	
           	TransferInfo ti_query=new TransferInfo();
           	ti_query.setAuditStatus(0);//待审核的同名转账申请
        	Page<TransferInfo> pages_ti = transferInfoService.findAll(0, 10000,"id", "asc", ti_query);
    		
    		JSONObject datas = new JSONObject();
			datas.put("dshyh", pages_ui.getContent().size());
			datas.put("rjdsp", pages_deposit.getContent().size());
			datas.put("cjdsp", pages_withdraw.getContent().size());
			datas.put("tmzhsp", pages_ta.getContent().size());
			datas.put("tmzzsp", pages_ti.getContent().size());
        	return ResponseDataUtil.buildSuccess(datas);
    	} catch (Exception e) {
        	e.printStackTrace();
        	return ResponseDataUtil.buildError(e.getMessage());
    	}
	}
	
	 @PostMapping("/exportUserExcel")
	    public ResponseData exportUserExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = { "userName", "realName", "mobile","creatTime","commissionAccount","superiorAgent","invitationCode"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*256);
	        sheet.setColumnWidth(3, (short)15*656);
	        sheet.setColumnWidth(4, (short)15*256);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
				String sort = request.getParameter("sort");
				// 获取分页控件的信息
				String sortBy = "desc";
				if (sort != null) {
					if (sort.startsWith("+")) {
						sortBy = "asc";
					}
					sort = sort.replace("+", "").replace("-", "");
				}
				User query = new User();
				query.setGmtCreateBegin(request.getParameter("gmtCreateBegin"));
				query.setGmtCreateEnd(request.getParameter("gmtCreateEnd"));
				query.setUsername(request.getParameter("username"));
				query.setNickName(request.getParameter("nickName"));
				
				
				if (!StringUtils.isEmpty(request.getParameter("isAvailable"))) {
					query.setIsAvailable(Integer.parseInt(request.getParameter("isAvailable")));
				}
				
				if (!StringUtils.isEmpty(request.getParameter("storeId"))) {
					query.setStoreId(Long.parseLong(request.getParameter("storeId")));
				}
				if (!StringUtils.isEmpty(request.getParameter("departmentId"))) {
					query.setDepartmentId(Long.parseLong(request.getParameter("departmentId")));
				}
				
				if (!StringUtils.isEmpty(request.getParameter("reId"))) {
					query.setReId(Long.parseLong(request.getParameter("reId")));
				}
				
				
				if (!StringUtils.isEmpty(request.getParameter("parent_ID"))) {
		       		 User loginUsersss = this.userService.findByUserName(request.getParameter("parent_ID"));
		       		 query.setReId(loginUsersss.getUserId()) ;
		          	}
				query.setRoleType(-99);
				  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
		             if(loginUser!=null) {
		            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
		            	 if(uu!=null&&uu.getUserId()!=null) {
		            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
		            			 //1.查出自己及名下所有的代理
		            			 query.setSortStr(uu.getSortStr());
		            		 }
		            	 }
		             }
				Page<User> pages = userService.findAll(0, 10000, sort, sortBy, query);
				List<LoginUser> userModelList = new ArrayList();
				for (int i = 0; i < pages.getContent().size(); i++) {
					User user = pages.getContent().get(i);
					LoginUser um = new LoginUser();
					List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
					List<String> roles = new ArrayList();
					for (IUserRole urm : iurList) {
						roles.add(urm.getRole());
					}
					um.setUserId(user.getUserId());
					um.setGmtCreateDate(user.getGmtCreate());
					um.setNickName(user.getNickName());
					um.setUsername(user.getUsername());
					um.setRolesNameArr(roles);
					um.setIsAvailable(user.getIsAvailable());
					um.setMobile(user.getMobile());
					um.setGmtCreateDateLong(um.getGmtCreateDate().getTime());
					um.setReId(user.getReId());
					um.setRoleType(user.getRoleType());
					um.setUserIcon(user.getUserIcon());
					um.setSaleSerial(user.getSaleSerial());
					um.setStoreId(user.getStoreId());
				
					if(um.getReId()!=null&&um.getReId().intValue()!=0&&um.getReId().intValue()!=1) {
						
						User parent_user=(User)this.userService.findUserById(um.getReId());
						
						if(parent_user!=null) {
							um.setStoreName(parent_user.getUsername()+"("+parent_user.getNickName()+")");
						}
						
					}else if(user.getReId().intValue()==1) {
						um.setStoreName(" ");
					}
					RuleUserMap rgm_query=new RuleUserMap();
					rgm_query.setUserId(um.getUserId());
					Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
					um.setRuleList(rgm_p.getContent());
					userModelList.add(um);
				}
			 for(int k=0;k<userModelList.size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   LoginUser luuuu=userModelList.get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(luuuu.getUsername()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(luuuu.getNickName()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(luuuu.getMobile()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(luuuu.getGmtCreateDate().toString()));
	               if(luuuu.getStoreId()!=null) {
	            	   row.createCell(4).setCellValue(new HSSFRichTextString(luuuu.getStoreId().toString()));
	               }else {
	            	   row.createCell(4).setCellValue(new HSSFRichTextString(""));
	               }
	             
	               row.createCell(5).setCellValue(new HSSFRichTextString(luuuu.getStoreName()));
	               row.createCell(6).setCellValue(new HSSFRichTextString(luuuu.getSaleSerial()));
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}
	 
	 
	 @PostMapping("/exportAchievementExcel")
	    public ResponseData exportAchievementExcel(HttpServletRequest request) {
	    		JSONObject datas = new JSONObject();
	    	 try {
	    		 String filename=new Date().getTime()+".xls";
	    	     String path=constantConfig.getFileStoreUrl();
			String[] headers = { "userName", "realName", "agentsQty","usersQty","accountsQty","balance","depositAmount","withdrawAmount"};
	        // 声明一个工作薄
	        HSSFWorkbook workbook = new HSSFWorkbook();
	        // 生成一个表格
	        HSSFSheet sheet = workbook.createSheet();
	        // 设置表格默认列宽度为15个字节
	        //sheet.setDefaultColumnWidth((short)3);
	        sheet.setColumnWidth(0,(short)15*256);
	        sheet.setColumnWidth(1, (short)15*256);
	        sheet.setColumnWidth(2,(short)15*256);
	        sheet.setColumnWidth(3, (short)15*256);
	        sheet.setColumnWidth(4, (short)15*256);
	        sheet.setColumnWidth(5, (short)15*256);
	        sheet.setColumnWidth(6, (short)15*256);
	        sheet.setColumnWidth(7, (short)15*256);
	        sheet.setDefaultRowHeight((short)400);
	       
	        HSSFRow row = sheet.createRow(0);
	        for (short i = 0; i < headers.length; i++) {
	            HSSFCell cell = row.createCell(i);
	            HSSFRichTextString text = new HSSFRichTextString(headers[i]);
	            HSSFCellStyle style = workbook.createCellStyle();
	             //设置背景颜色
	             style.setFillForegroundColor((short)10);
	            cell.setCellStyle(style);
	             cell.setCellValue(text);
	        }
			    int index = 0;
			    String sort = request.getParameter("sort");
				// 获取分页控件的信息
				String sortBy = "desc";
				if (sort != null) {
					if (sort.startsWith("+")) {
						sortBy = "asc";
					}
					sort = sort.replace("+", "").replace("-", "");
				}
				
				User query = new User();
				query.setGmtCreateBegin(request.getParameter("gmtCreateBegin"));
				query.setGmtCreateEnd(request.getParameter("gmtCreateEnd"));
				query.setUsername(request.getParameter("username"));
				query.setNickName(request.getParameter("nickName"));
				
				
				if (!StringUtils.isEmpty(request.getParameter("isAvailable"))) {
					query.setIsAvailable(Integer.parseInt(request.getParameter("isAvailable")));
				}
				
				if (!StringUtils.isEmpty(request.getParameter("storeId"))) {
					query.setStoreId(Long.parseLong(request.getParameter("storeId")));
				}
				if (!StringUtils.isEmpty(request.getParameter("departmentId"))) {
					query.setDepartmentId(Long.parseLong(request.getParameter("departmentId")));
				}
				
				if (!StringUtils.isEmpty(request.getParameter("reId"))) {
					query.setReId(Long.parseLong(request.getParameter("reId")));
				}
				
				query.setRoleType(-99);
				
				
				  User loginUser = this.userService.findByUserName(this.loginService.getCurrentUserName());
		             if(loginUser!=null) {
		            	 User uu=(User)this.userService.findUserById(loginUser.getUserId());
		            	 if(uu!=null&&uu.getUserId()!=null) {
		            		 if(uu.getRoleType().intValue()!=0&&uu.getRoleType().intValue()!=999) {//是销售或者代理
		            			 //1.查出自己及名下所有的代理
		            			 query.setSortStr(uu.getSortStr());
		            		 }
		            	 }
		             }

				Page<User> pages = userService.findAll(0, 10000, sort, sortBy, query);

				List<LoginUser> userModelList = new ArrayList();
				for (int i = 0; i < pages.getContent().size(); i++) {
					User user = pages.getContent().get(i);
					LoginUser um = new LoginUser();
					List<IUserRole> iurList = userService.findUserRoleByUserName(user.getUsername());
					List<String> roles = new ArrayList();
					for (IUserRole urm : iurList) {
						roles.add(urm.getRole());
					}
					um.setUserId(user.getUserId());
					um.setGmtCreateDate(user.getGmtCreate());
					um.setNickName(user.getNickName());
					um.setUsername(user.getUsername());
					um.setRolesNameArr(roles);
					um.setIsAvailable(user.getIsAvailable());
					um.setMobile(user.getMobile());
					um.setGmtCreateDateLong(um.getGmtCreateDate().getTime());
					um.setReId(user.getReId());
					um.setRoleType(user.getRoleType());
					um.setUserIcon(user.getUserIcon());
					um.setSaleSerial(user.getSaleSerial());
					um.setStoreId(user.getStoreId());
					RuleUserMap rgm_query=new RuleUserMap();
					rgm_query.setUserId(um.getUserId());
					Page<RuleUserMap> rgm_p=this.ruleUserMapService.findAll(0,10000,"id", "asc", rgm_query);
					
					um.setRuleList(rgm_p.getContent());
					
					//名下代理数
					User query_u = new User();
					query_u.setSortStr(user.getSortStr());
					Page<User> pages_u = userService.findAll(0, 10000, sort, sortBy, query_u);
					um.setAgentQty(pages_u.getContent().size());
					
					//名下CRM用户数
					
					 UserInfo query_ui=new UserInfo();
					 User user_query=new User();
	    			 user_query.setSortStr(user.getSortStr());
	    			 Page<User> ul=this.userService.findAll(0, 10000, "userId", "asc", user_query);
	    			 List userList=new ArrayList();
	    			 userList.add(user.getUserId());
	    			 for(int n=0;n<ul.getContent().size();n++) {
	    				 User user_1=ul.getContent().get(n);
	    				 userList.add(user_1.getUserId());
	    			 }
	    			 query_ui.setUserInfoList(userList);
	    			 Page<UserInfo> pages_ui = userInfoService.findAll(0, 10000, "id", "asc", query_ui);
	    			 um.setUserInfoQty(pages_ui.getContent().size());
	    			 
	    			 
	    			 
	    			 //根据CRM用户查出入金
	    			 
	    			 um.setFundAmountDepositTotal(0d);
	    			 um.setFundAmountWithdrawTotal(0d);
	    			 for(int mm=0;mm<pages_ui.getContent().size();mm++) {
	    				 UserInfo uu_i=(UserInfo)pages_ui.getContent().get(mm);
	    				 Double temp_fdt=this.dataSqlService.getFundTotal(uu_i.getId(),1);//入金
	    				 Double temp_fwt=this.dataSqlService.getFundTotal(uu_i.getId(),2);//出金
	    				 
	    				 if(um.getFundAmountDepositTotal()!=null) {
	    					 um.setFundAmountDepositTotal(um.getFundAmountDepositTotal()+temp_fdt);
	    				 }else {
	    					 um.setFundAmountDepositTotal(temp_fdt);
	    				 }
	    				 
	    				 
	    				 if(um.getFundAmountWithdrawTotal()!=null) {
	    					 um.setFundAmountWithdrawTotal(um.getFundAmountWithdrawTotal()+temp_fwt);
	    				 }else {
	    					 um.setFundAmountWithdrawTotal(temp_fwt);
	    				 }
	    				 
	    				 
	    			 }
					
					
					//名下交易账户数 及余额总值
	    			 TradeAccount query_ta=new TradeAccount();
	    			 List userList2=new ArrayList();
	    			 for(int m1=0;m1<pages_ui.getContent().size();m1++) {
	    				 UserInfo ui_2=(UserInfo)pages_ui.getContent().get(m1);
	    				 userList2.add(ui_2.getId());
	    			 }
	    			 
	    			 if(pages_ui.getContent().size()<=0) {
	    				 userList2.add(999999L);
	    			 }
	    			 query_ta.setUserInfoList(userList2);
	    			 Page<TradeAccount> pages_ta = tradeAccountService.findAll(0, 10000, "id", "asc", query_ta);
	    			 DecimalFormat df3=new DecimalFormat("000.00");
	    			 um.setTradeAccountAmount(0D);
	    			 for(int mm=0;mm<pages_ta.getContent().size();mm++) {
	    				 TradeAccount ttta=(TradeAccount)pages_ta.getContent().get(mm);
	    				 if(ttta.getBalance1()!=null) {
	    						 um.setTradeAccountAmount(new Double(df3.format(um.getTradeAccountAmount()+ttta.getBalance1())));
	    				 }
	    			 }
					um.setTradeAccountQty(pages_ta.getContent().size());
					userModelList.add(um);
				}
			 for(int k=0;k<userModelList.size();k++) {
				 index++;
				 try {
				   row = sheet.createRow(index);
				   LoginUser luuuu=userModelList.get(k);
				   row.createCell(0).setCellValue(new HSSFRichTextString(luuuu.getUsername()));
				   row.createCell(1).setCellValue(new HSSFRichTextString(luuuu.getNickName()));
	               row.createCell(2).setCellValue(new HSSFRichTextString(luuuu.getAgentQty().toString()));
	               row.createCell(3).setCellValue(new HSSFRichTextString(luuuu.getUserInfoQty().toString()));
	               row.createCell(4).setCellValue(new HSSFRichTextString(luuuu.getTradeAccountQty().toString()));
	               row.createCell(5).setCellValue(new HSSFRichTextString(luuuu.getTradeAccountAmount().toString()));
	               row.createCell(6).setCellValue(new HSSFRichTextString(luuuu.getFundAmountDepositTotal().toString()));
	               row.createCell(7).setCellValue(new HSSFRichTextString(luuuu.getFundAmountWithdrawTotal().toString()));
	               
				 } catch (Exception e) {
	                 // TODO Auto-generated catch block
	                 e.printStackTrace();
	             } 
			 }
			 FileOutputStream output=new FileOutputStream(path+filename);  
			 workbook.write(output);//写入磁盘  
	         workbook.close();
	         output.close();
	         datas.put("fileUrl",filename);
	         return ResponseDataUtil.buildSuccess(datas);
	    	 }catch (Exception e) {
	          	e.printStackTrace();
	          	return ResponseDataUtil.buildError(e.getMessage());
	      	}
		}
}
