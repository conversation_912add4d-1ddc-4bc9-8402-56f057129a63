package com.ews.common;

import java.net.URI;
import java.util.ArrayList;
import java.util.Date;
import java.util.HashMap;
import java.util.List;
import java.util.Random;

import org.java_websocket.WebSocket.READYSTATE;
import org.java_websocket.drafts.Draft_6455;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.data.domain.Page;
import org.springframework.stereotype.Component;

import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.ServerSetting;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.ServerSettingService;
import com.ews.crm.service.TradeAccountService;
import com.ews.crm.service.UserInfoService;

@Component
public class UpdateUserRecord {
	
	private TradeAccountService tradeAccountService;
	
	private OrderInfoService orderInfoService;
	
	private UserInfoService userInfoService;
	
	private ServerSettingService serverSettingService;
	
	
	
	
	
	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}





	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}





	public OrderInfoService getOrderInfoService() {
		return orderInfoService;
	}





	public void setOrderInfoService(OrderInfoService orderInfoService) {
		this.orderInfoService = orderInfoService;
	}





	public UserInfoService getUserInfoService() {
		return userInfoService;
	}





	public void setUserInfoService(UserInfoService userInfoService) {
		this.userInfoService = userInfoService;
	}





	public ServerSettingService getServerSettingService() {
		return serverSettingService;
	}





	public void setServerSettingService(ServerSettingService serverSettingService) {
		this.serverSettingService = serverSettingService;
	}





	public synchronized  void toUpdate(String login,String type) {
		
		
		     ServerSetting ss=(ServerSetting)this.getServerSettingService().findById(1L);
		     ss.setApiPort("8060");
		    //查询余额及信用
		    try {
		    	Random rn=new Random();
        		Long rrn=new Long(rn.nextInt(300))+700L;
        		Thread.sleep(rrn);
		    	//System.out.println("查询的余额及信用");
		    	MyWebsocketClient4SingleBalance client= new MyWebsocketClient4SingleBalance(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
			    client.setTradeAccountService(this.getTradeAccountService());
			    client.connect();
			    Thread.sleep(300);
				 if (client.getReadyState().equals(READYSTATE.OPEN)) {
					    System.out.println("getBalance(TS)   "+new Date()+" "+client.getReadyState());
					    HashMap map=new HashMap();
						map.put("reqtype", "getuserinfo");
						map.put("reqid", String.valueOf(new Date().getTime()));
						map.put("login", new Integer(login).intValue());
					    JSONObject jsonObj=new JSONObject(map);
					    client.send(jsonObj.toString());
				 }else {
					 
					 System.out.println(" NOT getBalance(TS)   "+new Date()+" "+client.getReadyState());
					 client.close();
				 }
				 
				}catch(Exception e) {
					System.out.println(e);
			    }
		    
		       //查询预付款
		       try {
		    	Thread.sleep(1100);
		    	//System.out.println("查询预付款");
		    	MyWebsocketClient4SingleBalance2 client2= new MyWebsocketClient4SingleBalance2(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
		   		        client2.setTradeAccountService(this.getTradeAccountService());
		   		    	client2.connect();
		   		    	Thread.sleep(300);
						if (client2.getReadyState().equals(READYSTATE.OPEN)) {
							System.out.println("getBalance2(TS)   "+new Date()+" "+client2.getReadyState());
							java.util.List list2=new ArrayList();
				    		HashMap mm1=new HashMap();
				    		mm1.put("login", new Integer(login).intValue());
				    		list2.add(mm1);
				    		HashMap map=new HashMap();
						    map.put("reqtype", "marginleveluserinfo");
							map.put("reqid", String.valueOf(new Date().getTime()));
							map.put("loginItem", list2);
							map.put("count", 1);
						    JSONObject jsonObj=new JSONObject(map);
						    client2.send(jsonObj.toString());
						}else {
							 System.out.println(" NOT getBalance2(TS)   "+new Date()+" "+client2.getReadyState());
							 client2.close();
						 }
	   			}catch(Exception e) {
	   				System.out.println(e);
	   		    }
		       /*
		       //查询持仓
		       if(type.equals("Trade")) {
		    	   try {
			    	   Thread.sleep(1200);
			    	   //System.out.println("查询的持仓");
			    	   MyWebsocketClient4SingleHold client= new MyWebsocketClient4SingleHold(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
						   client.setTradeAccountService(this.getTradeAccountService());
						   client.setOrderInfoService(this.getOrderInfoService());
						   client.connect();
						   Thread.sleep(300);
							if (client.getReadyState().equals(READYSTATE.OPEN)) {
								    System.out.println("queryHold(TS)   "+new Date()+" "+client.getReadyState());
								    HashMap map=new HashMap();
								    map.put("reqtype", "ordersuserinfo");
									map.put("reqid", String.valueOf(new Date().getTime()));
									map.put("login", new Integer(login));
								    JSONObject jsonObj=new JSONObject(map);
								    client.send(jsonObj.toString());
								}else {
									 System.out.println(" NOT queryHold(TS)   "+new Date()+" "+client.getReadyState());
									 client.close();
								 }
							 
					}catch(Exception e) {
						System.out.println(e);
								
					}
		       }
		       */ 
		      
		       //查询历史订单
		       if(type.equals("Trade")) {
		    	   
		    	   try {
			    	    Thread.sleep(1300);
			    	  //  System.out.println("查询历史订单");
			    	    MyWebsocketClient4SingleHistory client= new MyWebsocketClient4SingleHistory(new URI("ws://"+ss.getApiAddress()+":"+ss.getApiPort()), new Draft_6455(), null, 0);
			            client.setTradeAccountService(this.getTradeAccountService());
						client.setOrderInfoService(this.getOrderInfoService());
			     		client.connect();
			     		Thread.sleep(300);
			   		   if (client.getReadyState().equals(READYSTATE.OPEN)) {
			   			System.out.println("queryHistory(TS)   "+new Date()+" "+client.getReadyState());
			   		  HashMap map=new HashMap();
			  		    map.put("reqtype", "historyorderinfo");
			  			map.put("reqid", String.valueOf(new Date().getTime()));
			  			map.put("login", new Integer(login));
			  			OrderInfo order_query=new OrderInfo();
						order_query.setLoginId(login);
						order_query.setStatus(2);
						List ll=new ArrayList();
				    	ll.add(1);
				    	ll.add(0);
				    	ll.add(6);
				    	order_query.setTypeList(ll);
						Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,1, "closeTime","desc", order_query);
						
						
						TradeAccount ta_query=new TradeAccount();
						ta_query.setTradeId(login);
					    Page<TradeAccount> page=this.tradeAccountService.findAll(0,1,"id","asc", ta_query);
					   
					    
					    if(page.getContent().size()>0)
					    {
					    	 TradeAccount ta=(TradeAccount)page.getContent().get(0);
					    	 if(oi_page.getContent().size()>0) {//有记录
									map.put("fromtime", new Long((oi_page.getContent().get(0).getCloseTime())+1L));
								}else {//没有交易信息
									map.put("fromtime", new Long((new Date().getTime()/1000)-3700L));
								//	map.put("fromtime", new Long((ta.getGmtCreate().getTime()/1000)-3700L));
								}
					  			map.put("endtime", (new Date().getTime()/1000)+86400L);  //结束时间增加一个小时计算
					  		    JSONObject jsonObj=new JSONObject(map);
					  		   client.send(jsonObj.toString());
					    }
			   			}else {
							 System.out.println(" NOT queryHistory(TS)   "+new Date()+" "+client.getReadyState());
							 client.close();
						 }
			   		   
			   		  
						
			     		}catch(Exception e) {
			     			System.out.println(e);
			     		}
		       }
		     
		      
		
	}
	
	

}
