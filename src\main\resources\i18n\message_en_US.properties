orderlist.title=Trade Records
orderlist.subtitle2=Search and analyze your trading history
orderlist.stats.totallots=Total Lots
orderlist.stats.totalpl=Total P/L
orderlist.stats.balance=Available Balance
orderlist.filters.advanced=Advanced Filters
orderlist.filters.reset=Reset Filters
orderlist.filters.search=Search
orderlist.filters.startdate=Start Date
orderlist.filters.enddate=End Date
orderlist.filters.account=Trading Account
orderlist.filters.allaccounts=All Accounts
orderlist.filters.symbol=Symbol
orderlist.filters.allsymbols=All Symbols
orderlist.filters.direction=Direction
orderlist.filters.alldirections=All Directions
orderlist.filters.orderno=Order No.
orderlist.filters.placeholder.date=yyyy/mm/dd
orderlist.filters.placeholder.orderno=Enter order number
orderlist.table.title=Trade Records
orderlist.table.found=Found
orderlist.table.records=records
orderlist.table.showrange=Showing 1-50
orderlist.table.orderid=Order No.
orderlist.table.account=Account
orderlist.table.time=Time
orderlist.table.symbol=Symbol
orderlist.table.direction=Direction
orderlist.table.volume=Volume
orderlist.table.openprice=Open Price
orderlist.table.closeprice=Close Price
orderlist.table.profit=Profit
orderlist.table.status=Status
orderlist.status.open=Open
orderlist.status.closed=Closed
orderlist.empty.title=No trade records
orderlist.empty.desc=Adjust filters and try again
orderlist.page.first=First
orderlist.page.prev=Prev
orderlist.page.next=Next
orderlist.page.last=Last
orderlist.table.show0=Showing 0
orderlist.table.showprefix=Showing 
orderlist.table.showsuffix=
orderlist.direction.buy=Buy
orderlist.direction.sell=Sell


# Deposit status text
deposit.status.pending=Processing
deposit.status.completed=Completed
deposit.status.rejected=Rejected

login.title=Login to your account
login.email=Email address
login.enteremail=Enter your email
login.password=Password
login.enterpassword=Enter your password
login.signin=Sign in
login.signup=Sign up
login.forgotpassword=Reset Password
login.prompt1=Don not have account yet?
login.prompt2=Please enter the correct email account and password\uFF01
login.prompt3=Email account or password error, please re-enter!
login.prompt4=Successful login!


register.title=Create New Account
register.email=Email Address*
register.enteremail=Enter your email
register.password=Password*
register.enterpassword=Enter your password
register.confirm=Confirm New Password*
register.enterconfirm=Confirm your password
register.invitationcode=Invitation Code
register.language=Preferred Language
register.validatecode=Verification Code*
register.sendcode=Send Code
register.submit=Register
register.signin=Sign in
register.alreadaccount=Already have account? 
register.prompt1=Bad address syntax
register.prompt2=The email address has been used previously.
register.prompt3=The verification code has been sent to your email
register.prompt4=Please input a correct email address
register.prompt5=A valid password must contain 6 or more characters and at least one number or special character
register.prompt6=Password mismatched. Please re-enter password.
register.prompt7=Click Send Code to send a verification code to your email address.
register.prompt8=Enter verification code
register.prompt9=Wrong Verification Code
register.prompt10=The email address have been used.
register.prompt11=Register Success!! Please Login
register.prompt12=Wrong Verification Code
register.prompt13=Click Send Code to send a verification code to your email address.



changelanguage.title=Preferred Language
changelanguage.select=Select Language
changelanguage.submit=Submit


modifyPwd.title=Modify password
modifyPwd.oldpassword=Old Password
modifyPwd.newpassword=New Password
modifyPwd.confirm=Confirm Password
modifyPwd.submit=Submit
modifyPwd.prompt1=A valid password must: 1. Contain 6 or more characters  2. Contain at least one number or special character
modifyPwd.prompt2=Please enter your old password
modifyPwd.prompt3=Please enter a new Password
modifyPwd.prompt4=Password mismached, please re-enter.
modifyPwd.prompt5=Your request has been completed.
modifyPwd.prompt6=Your password was incorrect, please re-enter


menu.home=Home
menu.fundtransfer=Fund Transfer
menu.profile=Profile
menu.support=Support
menu.language=Preferred Language
menu.modifypassword=Modify Password
menu.logout=Logout




main.home=Home
main.overview=Overview
main.addaccount=Add Account
main.assetoverview=Asset Overview
main.total=Total
main.account=Account
main.balance=Balance
main.credit=Credit
main.equity=Equity
main.leverage=Leverage
main.deposit=Deposit
main.withdraw=Withdraw
main.prompt1=Cumulative total return
main.prompt2=(Period: Last 30 days)
main.advertisement=Promotion
main.fundrecord=Fund Record
main.type=Type
main.amount=Amount
main.time=Time
main.addnewaccount=Add New Account
main.mainaccount=Account Type
main.leverage=Leverage
main.password=Password
main.prompt3=Please enter a password for the new account
main.cancel=Cancel
main.createaccount=Create Account
main.prompt4=The password cannot be less than 8 digits and is a combination of letters and numbers
main.prompt5=Please enter the account password
main.prompt6=Please note that your request has been accepted and is under processing.
main.prompt7=Choose leverage
main.prompt8=Please ensure the information provided is the same as your identification document, these information can not be changed once it is submitted. If your name on your documents is in English, please key in your name in English.
main.totalassets=Total Assets
main.availablebalance=Available Balance
main.activeaccounts=Active Accounts
main.accountmanagement=Account Management
main.operations=Operations
main.language=Language
main.modifypassword=Change Password
main.logout=Logout
main.charttimeperiod.7days=7 Days
main.charttimeperiod.30days=30 Days
main.charttimeperiod.90days=90 Days
main.charttimeperiod.1year=1 Year
main.passwordrules=Password must contain at least one uppercase letter, one lowercase letter, one number and at least one symbol except #, with a length between 8 and 16 characters

gobal.internaltransfer=Internal transfer
gobal.prompt1=Transfer funds between accounts
gobal.deposit=Deposit
gobal.prompt2=Deposit funds into your account
gobal.withdraw=Withdraw
gobal.prompt3=Withdraw your funds


tradeaccount.Back=Back
tradeaccount.accountdetails=Account Details
tradeaccount.accountinformation=Account Information
tradeaccount.mt5account=MT4 Account
tradeaccount.accountype=Account Type
tradeaccount.balance=Balance
tradeaccount.credits=Credits
tradeaccount.equity=Equity
tradeaccount.leverage=Leverage
tradeaccount.changeleverage=Change Leverage
tradeaccount.resetpassword=Reset Password
tradeaccount.prompt1=Cumulative total return
tradeaccount.prompt2=Period: Last 30 days
tradeaccount.resetpassword=Reset Password
tradeaccount.newpassword=New Password
tradeaccount.prompt3=Please enter a new password to reset 
tradeaccount.close=Close
tradeaccount.submit=Submit
tradeaccount.prompt4=Your request has been submitted, please refresh the data later.
tradeaccount.prompt5=Please enter a new password to reset
tradeaccount.prompt6=Your request has been submitted, please refresh the data later.
tradeaccount.prompt7=The password cannot be less than 8 digits and is a combination of letters and numbers



fundtransfer.title=Fund Transfer
fundtransfer.overview=Overview
fundtransfer.assetoverview=Asset OverView
fundtransfer.account=Account
fundtransfer.type=Type
fundtransfer.balance=Balance
fundtransfer.credit=Credit
fundtransfer.equity=Equity
fundtransfer.leverage=Leverage



transfer.title=Fund Transfer
transfer.internalTransfer=Internal Transfer
transfer.account=Account
transfer.type=Type
transfer.balance=Balance
transfer.credit=Credit
transfer.equity=Equity
transfer.leverage=Leverage
transfer.fromaccount=From Account
transfer.toaccount=To Account
transfer.amount=Amount
transfer.confirm=Confirm
transfer.prompt1=Select account
transfer.prompt2=Unable to transfer due to same account
transfer.prompt3=Enter amount to transfer
transfer.prompt4=Invalid amount
transfer.prompt5=Maximum Amount
transfer.prompt6=Request is being processed
transfer.selectsourceaccount=Select Source Account
transfer.selecttargetaccount=Select Target Account
transfer.note=Transfer Note (Optional)
transfer.noteplaceholder=Please enter transfer note
transfer.recentoperations=Recent Operations
transfer.depositsuccess=Deposit Success
transfer.withdrawsuccess=Withdrawal Success


deposit.title=Fund Transfer
deposit.deposit=Deposit
deposit.accountnumber=Account Number
deposit.paymentto=Payment Method
deposit.depositamount=Deposit Amount
deposit.notes=Notes
deposit.submit=Submit
deposit.prompt1=Enter amount
deposit.prompt2=Invalid amount
deposit.prompt3=Minimum Amount (Per Transaction)
deposit.prompt4=Maximum Amount (Per Transaction)



withdraw.title=Fund Transfer
withdraw.withdraw=Withdraw
withdraw.accountnumber=Account Number
withdraw.withdrawamount=Withdraw Amount
withdraw.prompt5=Do note that is you have insufficient margin, opened position will be closed
withdraw.withdrawcurrency=Withdraw Currency
withdraw.bankname=Bank Name
withdraw.bankcoutry=Bank Country
withdraw.bankaccount=Bank Account
withdraw.bankswift=Bank Swift
withdraw.notes=Notes
withdraw.submit=Submit
withdraw.prompt1=Enter amount
withdraw.prompt2=Please fill in all the payment information
withdraw.prompt3=Invalid amount
withdraw.prompt4=Maximum Amount
withdraw.recentwithdrawals=Recent Withdrawals




profile.title=Profile
profile.accountsettings=Account Settings
profile.unverified=Unverified
profile.verified=Verified
profile.autoverifying=Automated Verification
profile.manualreview=Manual Review
profile.reject=Reject
profile.crmaccount=Login Email
profile.firstname=First Name
profile.lastname=Last Name
profile.dob=DOB(YYYY-MM-DD) 
profile.citizenship=Citizenship
profile.xxxx=ID Number
profile.identification=Personal Information
profile.countryofresidence=Country of Residence
profile.city=Bank/Branch Name
profile.mobile=Mobile Number
profile.address=Bank Account
profile.uploaddocuments=Upload Documents
profile.personalidentification=Identity Card 
profile.upload=Upload
profile.proofofaddress=Bank Card
profile.bankstatement=Payment Code
profile.continueautoverify=Continue Auto Verify
profile.submit=Submit
profile.prompt1=Please complete the required fields!
profile.prompt2=Submit Successfully!



ext.prompt1=Please verify your profile before submitting the withdrawal request
ext.prompt2=A verification code has been successfully sent to your email address
ext.prompt3=Email Account Not Registered
ext.prompt4=Please enter a valid email address
ext.prompt5=Password must be at least 6 characters long
ext.prompt6=Please enter a new password
ext.prompt7=Password Mismatch
ext.prompt8=Please provide the verification code
ext.prompt9=Password reset successful
ext.prompt10=Verification code error
ext.prompt11=Please Contact Our Support Desk
ext.prompt12=You have hit the account type  maximum limit.
ext.prompt13=Creation Failed
ext.prompt14=Passwords must be 8-16 characters long containing only contain English letters or numbers. Symbols are not allowed
ext.prompt15=Your request has been successfully submitted. Please wait for approval.

# Additional translations
menu.fundrecords=Fund Records
menu.traderecords=Trade Records
menu.webtrader=WebTrader
common.baseinformation=Base Information
common.personalinformation=Personal Information
common.digitalcurrencyaddress=Digital Currency Address
common.uploaddocuments=Upload Documents
common.clicktoreplace=Click to replace
deposit.sufficientfunds=Please keep sufficient funds in your bank account, otherwise it may result in a rejection or overdraft.
date.selectstartdate=Select start date
date.selectenddate=Select end date

# Fund List page
fundlist.pagetitle=Fund Records
fundlist.subtitle=Deposit/Withdrawal Records
fundlist.cardtitle=Deposit/Withdrawal Records
fundlist.transactiontype=Transaction Type
fundlist.tradeaccount=Trade Account
fundlist.time=Time
fundlist.amount=Amount
fundlist.account=Account
fundlist.auditstatus=Audit Status
fundlist.processingstatus=Processing Status
fundlist.deposit=Deposit
fundlist.withdrawal=Withdrawal
fundlist.pendingaudit=Pending Audit
fundlist.approved=Approved
fundlist.rejected=Rejected
fundlist.pending=Pending
fundlist.processingsuccess=Processing Success
fundlist.processingfailed=Processing Failed

# Order List page
orderlist.pagetitle=Trade Records
orderlist.subtitle=Order History
orderlist.startdate=Start Date
orderlist.enddate=End Date
orderlist.search=Search
orderlist.clear=Clear
orderlist.ordernumber=Order Number
orderlist.tradeaccount=Trade Account
orderlist.closetime=Close Time
orderlist.closeamount=Close Amount
orderlist.tradetype=Trade Type
orderlist.volume=Volume
orderlist.profitloss=Profit/Loss
orderlist.buy=BUY
orderlist.sell=SELL
orderlist.totalrecords=Records
orderlist.totalvolume=Trade Volume
orderlist.totalprofitloss=Profit/Loss

# Pagination
pagination.showing=Showing
pagination.to=to
pagination.of=of
pagination.records=records
pagination.previous=Previous
pagination.next=Next
pagination.pagenav=Page navigation

# Modal and additional interface text
modal.accountbasicsettings=Account Basic Settings
modal.personalinformationcompletion=Personal Information Completion  
modal.tradeleveragesettings=Trade Leverage Settings
modal.setupaccountinfo=Setup your trading account information
modal.ensureinfoaccuracy=Please ensure the information provided is accurate
modal.generatepassword=Generate random password
modal.selectcountry=Please select country
modal.confirm=Confirm
common.ok=OK
common.required=*
common.optional=Optional

# Deposit page specific translations
deposit.pagesubtitle=Secure and fast fund management services
deposit.bankinformation=Receiving Bank Information
deposit.bankdetails=Receiving Bank Details
deposit.acceptedcurrency=Accepted Currency
deposit.accountholder=Account Holder
deposit.accountnumber.detail=Account Number
deposit.bankname=Bank Name
deposit.bankaddress=Bank Address
deposit.swiftcode=Swift Code
deposit.digitalcurrencyaddress=Digital Currency Address
deposit.uploadtransfervoucher=Upload Transfer Voucher
deposit.clicktoupload=Click to upload transfer voucher
deposit.supportedformats=Supports JPG, PNG, PDF formats, maximum 10MB
deposit.enteramount=Please enter deposit amount
deposit.enterdigitaladdress=Please enter digital currency address
deposit.enterremarks=Please enter remarks (optional)
deposit.accountbalance=Account Balance
deposit.availablebalance=Available Balance:
deposit.recentdeposits=Recent Deposits
deposit.norecords=No deposit records
deposit.fileuploadsuccessful=File uploaded successfully
deposit.uploadsuccessful=Upload successful


















