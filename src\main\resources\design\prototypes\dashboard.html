<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>USK外汇CRM - 账户总览</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Microsoft YaHei', -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background: linear-gradient(135deg, #0a0e14 0%, #1a1f2e 25%, #2a3441 50%, #1a1f2e 75%, #0a0e14 100%);
            color: #ffffff;
            overflow-x: hidden;
        }

        /* 顶部导航栏 */
        .navbar {
            background: rgba(26, 31, 46, 0.95);
            backdrop-filter: blur(20px);
            border-bottom: 1px solid rgba(0, 122, 255, 0.2);
            padding: 0 30px;
            height: 70px;
            display: flex;
            align-items: center;
            justify-content: space-between;
            position: sticky;
            top: 0;
            z-index: 1000;
        }

        .nav-left {
            display: flex;
            align-items: center;
        }

        .nav-logo {
            width: 40px;
            height: 40px;
            border-radius: 8px;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-right: 15px;
            overflow: hidden;
        }

        .nav-logo img {
            width: 100%;
            height: 100%;
            object-fit: contain;
        }

        .nav-title {
            font-size: 20px;
            font-weight: 500;
            letter-spacing: 1px;
        }

        .nav-menu {
            display: flex;
            align-items: center;
            gap: 30px;
            margin-left: 50px;
        }

        .nav-item {
            color: rgba(255, 255, 255, 0.7);
            text-decoration: none;
            padding: 8px 16px;
            border-radius: 8px;
            transition: all 0.3s ease;
            font-size: 14px;
        }

        .nav-item:hover, .nav-item.active {
            color: #007AFF;
            background: rgba(0, 122, 255, 0.1);
        }

        .nav-right {
            display: flex;
            align-items: center;
            gap: 20px;
        }

        .nav-user {
            display: flex;
            align-items: center;
            gap: 10px;
            padding: 8px 16px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 20px;
            cursor: pointer;
        }

        .user-avatar {
            width: 32px;
            height: 32px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border-radius: 50%;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 12px;
            font-weight: bold;
        }

        /* 主要内容区域 */
        .main-container {
            padding: 30px;
            min-height: calc(100vh - 70px);
        }

        /* 数据统计卡片 */
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(280px, 1fr));
            gap: 20px;
            margin-bottom: 30px;
        }

        .stat-card {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 25px;
            position: relative;
            overflow: hidden;
            transition: all 0.3s ease;
        }

        .stat-card:hover {
            border-color: rgba(0, 122, 255, 0.4);
            transform: translateY(-2px);
            box-shadow: 0 20px 40px rgba(0, 0, 0, 0.3);
        }

        .stat-card::before {
            content: '';
            position: absolute;
            top: 0;
            left: 0;
            right: 0;
            height: 3px;
            background: linear-gradient(90deg, #007AFF, #00FF88);
            opacity: 0.8;
        }

        .stat-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 15px;
        }

        .stat-title {
            color: rgba(255, 255, 255, 0.7);
            font-size: 14px;
            font-weight: 500;
        }

        .stat-icon {
            width: 40px;
            height: 40px;
            background: rgba(0, 122, 255, 0.1);
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 18px;
        }

        .stat-value {
            font-size: 32px;
            font-weight: 700;
            color: #ffffff;
            margin-bottom: 8px;
            font-family: 'Courier New', monospace;
        }

        .stat-change {
            font-size: 14px;
            display: flex;
            align-items: center;
            gap: 5px;
        }

        .change-positive { color: #00FF88; }
        .change-negative { color: #FF3B30; }

        /* 图表区域 */
        .chart-section {
            display: grid;
            grid-template-columns: 2fr 1fr;
            gap: 20px;
            margin-bottom: 30px;
        }

        .chart-card {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            padding: 25px;
            position: relative;
        }

        .chart-header {
            display: flex;
            justify-content: space-between;
            align-items: center;
            margin-bottom: 20px;
        }

        .chart-title {
            font-size: 18px;
            font-weight: 600;
            color: #ffffff;
        }

        .chart-controls {
            display: flex;
            gap: 10px;
        }

        .chart-control {
            padding: 6px 12px;
            background: rgba(0, 122, 255, 0.1);
            border: 1px solid rgba(0, 122, 255, 0.2);
            border-radius: 6px;
            color: rgba(255, 255, 255, 0.7);
            cursor: pointer;
            font-size: 12px;
            transition: all 0.3s ease;
        }

        .chart-control.active, .chart-control:hover {
            background: rgba(0, 122, 255, 0.3);
            color: #ffffff;
        }

        .chart-area {
            height: 300px;
            background: linear-gradient(135deg, rgba(0, 122, 255, 0.05), rgba(0, 255, 136, 0.05));
            border-radius: 12px;
            position: relative;
            overflow: hidden;
        }

        /* 模拟SVG图表 */
        .chart-svg {
            position: absolute;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
        }

        .chart-line {
            stroke: #007AFF;
            stroke-width: 3;
            fill: none;
            filter: drop-shadow(0 0 6px rgba(0, 122, 255, 0.6));
        }

        .chart-area-fill {
            fill: url(#gradient);
            opacity: 0.3;
        }

        /* 账户列表 */
        .accounts-section {
            background: rgba(26, 31, 46, 0.8);
            backdrop-filter: blur(20px);
            border: 1px solid rgba(0, 122, 255, 0.15);
            border-radius: 16px;
            overflow: hidden;
        }

        .section-header {
            padding: 25px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
            display: flex;
            justify-content: space-between;
            align-items: center;
        }

        .section-title {
            font-size: 20px;
            font-weight: 600;
            color: #ffffff;
        }

        .add-account-btn {
            padding: 10px 20px;
            background: linear-gradient(135deg, #007AFF, #00FF88);
            border: none;
            border-radius: 8px;
            color: white;
            font-size: 14px;
            font-weight: 500;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .add-account-btn:hover {
            transform: translateY(-1px);
            box-shadow: 0 10px 20px rgba(0, 122, 255, 0.4);
        }

        .accounts-table {
            width: 100%;
        }

        .table-header {
            background: rgba(0, 0, 0, 0.2);
            color: rgba(255, 255, 255, 0.8);
        }

        .table-header th {
            padding: 15px 25px;
            text-align: left;
            font-weight: 500;
            font-size: 14px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.1);
        }

        .table-row {
            transition: all 0.3s ease;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
        }

        .table-row:hover {
            background: rgba(0, 122, 255, 0.05);
        }

        .table-row td {
            padding: 20px 25px;
            font-size: 14px;
            vertical-align: middle;
        }

        .account-name {
            font-weight: 600;
            color: #ffffff;
        }

        .account-type {
            color: rgba(255, 255, 255, 0.6);
            font-size: 12px;
        }

        .balance-amount {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            color: #00FF88;
        }

        .leverage-badge {
            background: rgba(0, 122, 255, 0.2);
            color: #007AFF;
            padding: 4px 8px;
            border-radius: 4px;
            font-size: 12px;
            font-weight: 500;
        }

        .action-buttons {
            display: flex;
            gap: 10px;
        }

        .action-btn {
            padding: 6px 12px;
            border: 1px solid rgba(255, 255, 255, 0.2);
            border-radius: 6px;
            background: transparent;
            color: rgba(255, 255, 255, 0.7);
            font-size: 12px;
            cursor: pointer;
            transition: all 0.3s ease;
        }

        .action-btn:hover {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        .action-btn.primary {
            background: rgba(0, 122, 255, 0.2);
            border-color: #007AFF;
            color: #007AFF;
        }

        /* 右侧资金流动记录 */
        .activity-list {
            max-height: 350px;
            overflow-y: auto;
        }

        .activity-item {
            padding: 15px;
            border-bottom: 1px solid rgba(255, 255, 255, 0.05);
            display: flex;
            align-items: center;
            gap: 15px;
            transition: all 0.3s ease;
        }

        .activity-item:hover {
            background: rgba(0, 122, 255, 0.05);
        }

        .activity-icon {
            width: 40px;
            height: 40px;
            border-radius: 10px;
            display: flex;
            align-items: center;
            justify-content: center;
            font-size: 16px;
        }

        .activity-icon.deposit {
            background: rgba(0, 255, 136, 0.2);
            color: #00FF88;
        }

        .activity-icon.withdraw {
            background: rgba(255, 59, 48, 0.2);
            color: #FF3B30;
        }

        .activity-icon.transfer {
            background: rgba(0, 122, 255, 0.2);
            color: #007AFF;
        }

        .activity-info {
            flex: 1;
        }

        .activity-title {
            font-size: 14px;
            font-weight: 500;
            color: #ffffff;
            margin-bottom: 4px;
        }

        .activity-desc {
            font-size: 12px;
            color: rgba(255, 255, 255, 0.6);
        }

        .activity-amount {
            font-family: 'Courier New', monospace;
            font-weight: 600;
            font-size: 14px;
        }

        .amount-positive { color: #00FF88; }
        .amount-negative { color: #FF3B30; }

        /* 响应式设计 */
        @media (max-width: 1200px) {
            .chart-section {
                grid-template-columns: 1fr;
            }
        }

        @media (max-width: 768px) {
            .main-container {
                padding: 20px;
            }
            
            .stats-grid {
                grid-template-columns: 1fr;
            }
            
            .nav-menu {
                display: none;
            }
        }

        /* 滚动条样式 */
        ::-webkit-scrollbar {
            width: 6px;
        }

        ::-webkit-scrollbar-track {
            background: rgba(255, 255, 255, 0.1);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb {
            background: rgba(0, 122, 255, 0.5);
            border-radius: 3px;
        }

        ::-webkit-scrollbar-thumb:hover {
            background: rgba(0, 122, 255, 0.7);
        }
    </style>
</head>
<body>
    <!-- 顶部导航栏 -->
    <nav class="navbar">
        <div class="nav-left">
            <div class="nav-logo"><img src="logo.png" alt="USK"></div>
            <div class="nav-title">USK FOREX CRM</div>
            <div class="nav-menu">
                <a href="dashboard.html" class="nav-item active">账户总览</a>
                <a href="fund-management.html" class="nav-item">资金管理</a>
                <a href="trading-records.html" class="nav-item">交易记录</a>
                <a href="profile.html" class="nav-item">个人信息</a>
            </div>
        </div>
        <div class="nav-right">
            <div class="nav-user">
                <div class="user-avatar">张</div>
                <span>张先生</span>
            </div>
        </div>
    </nav>

    <!-- 主要内容区域 -->
    <div class="main-container">
        <!-- 数据统计卡片 -->
        <div class="stats-grid">
            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">总资产</span>
                    <div class="stat-icon">💰</div>
                </div>
                <div class="stat-value">$458,726.82</div>
               
            </div>

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">可用余额</span>
                    <div class="stat-icon">💳</div>
                </div>
                <div class="stat-value">$385,492.15</div>
               
            </div>

            

            <div class="stat-card">
                <div class="stat-header">
                    <span class="stat-title">活跃账户</span>
                    <div class="stat-icon">🔄</div>
                </div>
                <div class="stat-value">8</div>
                
            </div>
        </div>

         <!-- 账户列表 -->
         <div class="accounts-section">
            <div class="section-header">
                <h2 class="section-title">交易账户管理</h2>
                <button class="add-account-btn">+ 添加账户</button>
            </div>
            <table class="accounts-table">
                <thead class="table-header">
                    <tr>
                        <th>账户信息</th>
                        <th>账户类型</th>
                        <th>余额资金</th>
                        <th>信用资金</th>
                        <th>净资产</th>
                        <th>杠杆</th>
                        <th>操作</th>
                    </tr>
                </thead>
                <tbody>
                    <tr class="table-row">
                        <td>
                            <div class="account-name">MT5-********</div>
                            <div class="account-type">标准账户</div>
                        </td>
                        <td>标准</td>
                        <td class="balance-amount">$156,847.32</td>
                        <td class="balance-amount">$156,847.32</td>
                        <td class="balance-amount">$156,847.32</td>
                        <td><span class="leverage-badge">1:500</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn primary">入金</button>
                                <button class="action-btn">出金</button>
                                <button class="action-btn">详情</button>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td>
                            <div class="account-name">MT5-********</div>
                            <div class="account-type">ECN账户</div>
                        </td>
                        <td>ECN</td>
                        <td class="balance-amount">$89,325.15</td>
                        <td class="balance-amount">$89,325.15</td>
                        <td class="balance-amount">$89,325.15</td>
                        <td><span class="leverage-badge">1:200</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn primary">入金</button>
                                <button class="action-btn">出金</button>
                                <button class="action-btn">详情</button>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td>
                            <div class="account-name">MT5-********</div>
                            <div class="account-type">迷你账户</div>
                        </td>
                        <td>迷你</td>
                        <td class="balance-amount">$45,678.90</td>
                        <td class="balance-amount">$45,678.90</td>
                        <td class="balance-amount">$45,678.90</td>
                        <td><span class="leverage-badge">1:100</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn primary">入金</button>
                                <button class="action-btn">出金</button>
                                <button class="action-btn">详情</button>
                            </div>
                        </td>
                    </tr>
                    <tr class="table-row">
                        <td>
                            <div class="account-name">MT5-********</div>
                            <div class="account-type">VIP账户</div>
                        </td>
                        <td>VIP</td>
                        <td class="balance-amount">$298,547.26</td>
                        <td class="balance-amount">$298,547.26</td>
                        <td class="balance-amount">$298,547.26</td>
                        <td><span class="leverage-badge">1:1000</span></td>
                        <td>
                            <div class="action-buttons">
                                <button class="action-btn primary">入金</button>
                                <button class="action-btn">出金</button>
                                <button class="action-btn">详情</button>
                            </div>
                        </td>
                    </tr>
                </tbody>
            </table>
        </div>
        <br/>

        <!-- 图表区域 -->
        <div class="chart-section">
            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">总累计回报趋势</h3>
                    <div class="chart-controls">
                        <button class="chart-control active">7天</button>
                        <button class="chart-control">30天</button>
                        <button class="chart-control">90天</button>
                        <button class="chart-control">1年</button>
                    </div>
                </div>
                <div class="chart-area">
                    <svg class="chart-svg">
                        <defs>
                            <linearGradient id="gradient" x1="0%" y1="0%" x2="0%" y2="100%">
                                <stop offset="0%" style="stop-color:#007AFF;stop-opacity:0.3" />
                                <stop offset="100%" style="stop-color:#007AFF;stop-opacity:0" />
                            </linearGradient>
                        </defs>
                        <!-- 模拟图表路径 -->
                        <path class="chart-area-fill" d="M 0 250 L 50 230 L 100 240 L 150 220 L 200 210 L 250 195 L 300 180 L 350 170 L 400 160 L 450 150 L 500 140 L 550 130 L 600 120 L 650 110 L 700 100 L 700 250 Z"/>
                        <path class="chart-line" d="M 0 250 L 50 230 L 100 240 L 150 220 L 200 210 L 250 195 L 300 180 L 350 170 L 400 160 L 450 150 L 500 140 L 550 130 L 600 120 L 650 110 L 700 100"/>
                    </svg>
                </div>
            </div>

            <div class="chart-card">
                <div class="chart-header">
                    <h3 class="chart-title">资金流动日志</h3>
                </div>
                <div class="activity-list">
                    <div class="activity-item">
                        <div class="activity-icon deposit">↓</div>
                        <div class="activity-info">
                            <div class="activity-title">入金成功</div>
                            <div class="activity-desc">账户 MT5-001 • 2小时前</div>
                        </div>
                        <div class="activity-amount amount-positive">+$50,000</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon transfer">⇄</div>
                        <div class="activity-info">
                            <div class="activity-title">入金成功</div>
                            <div class="activity-desc">MT5-001 → MT5-002 • 4小时前</div>
                        </div>
                        <div class="activity-amount">$25,000</div>
                    </div>
                    
                    <div class="activity-item">
                        <div class="activity-icon deposit">↓</div>
                        <div class="activity-info">
                            <div class="activity-title">入金成功</div>
                            <div class="activity-desc">账户 MT5-002 • 2天前</div>
                        </div>
                        <div class="activity-amount amount-positive">+$30,000</div>
                    </div>
                    <div class="activity-item">
                        <div class="activity-icon transfer">⇄</div>
                        <div class="activity-info">
                            <div class="activity-title">入金成功</div>
                            <div class="activity-desc">MT5-003 → MT5-001 • 3天前</div>
                        </div>
                        <div class="activity-amount">$10,000</div>
                    </div>
                </div>
            </div>
        </div>

       
    </div>

    <script>
        // 模拟实时数据更新
        function updateRealTimeData() {
            // 更新统计数据
            const statValues = document.querySelectorAll('.stat-value');
            statValues.forEach(value => {
                const currentText = value.textContent;
                if (currentText.includes('$')) {
                    const currentValue = parseFloat(currentText.replace(/[$,]/g, ''));
                    const change = (Math.random() - 0.5) * 1000;
                    const newValue = currentValue + change;
                    value.textContent = '$' + newValue.toLocaleString('en-US', {
                        minimumFractionDigits: 2,
                        maximumFractionDigits: 2
                    });
                }
            });

            // 更新账户余额
            const balanceAmounts = document.querySelectorAll('.balance-amount');
            balanceAmounts.forEach(amount => {
                const currentValue = parseFloat(amount.textContent.replace(/[$,]/g, ''));
                const change = (Math.random() - 0.5) * 500;
                const newValue = currentValue + change;
                amount.textContent = '$' + newValue.toLocaleString('en-US', {
                    minimumFractionDigits: 2,
                    maximumFractionDigits: 2
                });
            });
        }

        // 图表控制切换
        document.querySelectorAll('.chart-control').forEach(control => {
            control.addEventListener('click', function() {
                document.querySelectorAll('.chart-control').forEach(c => c.classList.remove('active'));
                this.classList.add('active');
            });
        });

        // 页面加载完成后初始化
        document.addEventListener('DOMContentLoaded', function() {
            // 每30秒更新一次实时数据
            setInterval(updateRealTimeData, 30000);
        });
    </script>
</body>
</html> 