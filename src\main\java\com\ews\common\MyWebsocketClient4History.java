package com.ews.common;
import java.io.UnsupportedEncodingException;
import java.math.BigDecimal;
import java.net.URI;
import java.nio.ByteBuffer;
import java.util.Map;

import org.java_websocket.client.WebSocketClient;
import org.java_websocket.drafts.Draft;
import org.java_websocket.handshake.ServerHandshake;
import org.springframework.data.domain.Page;

import com.alibaba.fastjson.JSONArray;
import com.alibaba.fastjson.JSONObject;
import com.ews.crm.entity.OrderInfo;
import com.ews.crm.entity.TradeAccount;
import com.ews.crm.service.OrderInfoService;
import com.ews.crm.service.TradeAccountService;


public class MyWebsocketClient4History extends WebSocketClient {
	private TradeAccountService tradeAccountService;
	private OrderInfoService orderInfoService; 
	
	
	
	public OrderInfoService getOrderInfoService() {
		return orderInfoService;
	}

	public void setOrderInfoService(OrderInfoService orderInfoService) {
		this.orderInfoService = orderInfoService;
	}

	public TradeAccountService getTradeAccountService() {
		return tradeAccountService;
	}

	public void setTradeAccountService(TradeAccountService tradeAccountService) {
		this.tradeAccountService = tradeAccountService;
	}

	public MyWebsocketClient4History(URI serverUri, Draft protocolDraft, Map<String, String> httpHeaders, int connectTimeout) {
		super(serverUri, protocolDraft, httpHeaders, connectTimeout);
	}
 
	@Override
	public void onOpen(ServerHandshake arg0) {
		//System.out.println("打开链接");
	}
 
	@Override
	public void onMessage(String arg0) {
		if(arg0!=null){
			System.out.println(" YJS ");
			//System.out.println("收到消息" + arg0);  
			//以下为接收到消息后的处理
			JSONObject jsStr = JSONObject.parseObject(arg0);
			if(jsStr.getString("status").equals("0")) {
				close();
				return ;
			}
			if(jsStr.get("error").toString().equals("")) {
				try {
					
					JSONArray ja=JSONArray.parseArray(jsStr.getString("hisorders"));
					
					System.out.println("ja.size:"+ja.size());
					for(int m=0;m<ja.size();m++) {
						JSONObject ob=(JSONObject)ja.get(m);
						//System.out.println(ob.getString("order"));
					OrderInfo order_query=new OrderInfo();
					order_query.setOrderNo(ob.getString("order"));
					
					//System.out.println("历史订单ID:" + ob.getString("order"));  
					Page<OrderInfo> oi_page=this.orderInfoService.findAll(0,1, "id","asc", order_query);
					if(oi_page.getContent().size()>0) {//已存在
						OrderInfo oi=(OrderInfo)oi_page.getContent().get(0);
						if(oi.getStatus().intValue()!=2) {//如果是持仓订单则改为历史订单
						oi.setStorage(ob.getDouble("storage"));
						oi.setProfit(ob.getDouble("profit"));
						oi.setType(ob.getInteger("cmd"));
						oi.setVolume(new BigDecimal(((float)ob.getInteger("volume")/100)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
						oi.setCloseTime(ob.getInteger("close_time"));
						oi.setClosePrice(ob.getDouble("close_price"));
						oi.setStatus(2);
	                   this.orderInfoService.saveOrUpdate(oi);
						}
					}else {//未存在
						
						OrderInfo order_info=new OrderInfo();
						order_info.setOrderNo(ob.getString("order"));
						order_info.setOpenTime(ob.getInteger("open_time"));
						order_info.setOpenPrice(ob.getDouble("open_price"));
                        order_info.setType(ob.getInteger("cmd"));
                        order_info.setVolume(new BigDecimal(((float)ob.getInteger("volume")/100)).setScale(4,BigDecimal.ROUND_HALF_UP).doubleValue());
                        order_info.setSymbol(ob.getString("symbol"));
                        order_info.setStorage(ob.getDouble("storage"));
                        order_info.setProfit(ob.getDouble("profit"));
                        order_info.setStatus(2);
                        order_info.setIsRakeback(0);
                        order_info.setLoginId(jsStr.getString("login"));
                        
                        order_info.setCloseTime(ob.getInteger("close_time"));
                        order_info.setClosePrice(ob.getDouble("close_price"));
                        
                        
                        TradeAccount query=new TradeAccount();
    					query.setTradeId(jsStr.get("login").toString());
    					Page<TradeAccount>  ta_page=this.tradeAccountService.findAll(0,1,"id","asc", query);
    					if(ta_page.getContent().size()>0) {
    						order_info.setTradeId(ta_page.getContent().get(0).getId());
    					}
                        
                        this.orderInfoService.saveOrUpdate(order_info);
                        
					}
					}
					
				}catch(Exception e) {
					System.out.println(e);
				}
				}else {
					//  close();
				}
			
			
		}
		
		
		new Thread(){
    		public void run(){
    			try {
    				 Thread.sleep(60000L*50);  
    			      close();
    			}catch(Exception e) {
    				
    			}
    		}
    		}.start();
		
		
	}
 
	@Override
	public void onError(Exception arg0) {
		 close();
		System.out.println("发生错误已关闭");
	}
 
	@Override
	public void onClose(int arg0, String arg1, boolean arg2) {
		System.out.println("链接已关闭");
	}
 
	@Override
	public void onMessage(ByteBuffer bytes) {
		try {
			System.out.println(new String(bytes.array(), "utf-8"));
		} catch (UnsupportedEncodingException e) {
			 close();
			System.out.println("出现异常");
		}
	}
}
